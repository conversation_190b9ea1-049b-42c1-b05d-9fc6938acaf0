import axios, { type AxiosInstance, type AxiosError } from 'axios'
import type {
  UserResponse,
  CreateUserRequest,
  UpdateUserRequest,
  Customer,
  CreateCustomerRequest,
  UpdateCustomerRequest,
  CustomerPackage,
  CreateCustomerPackageRequest,
  UpdateCustomerPackageRequest,
  CustomerDocument,
  CreateCustomerDocumentRequest,
  UpdateCustomerDocumentRequest,
  CustomerServices,
  CreateCustomerServicesRequest,
  UpdateCustomerServicesRequest,
  Package,
  CreatePackageRequest,
  UpdatePackageRequest,
  Equipment,
  CreateEquipmentRequest,
  UpdateEquipmentRequest,
  EquipmentInventory,
  CreateEquipmentInventoryRequest,
  UpdateEquipmentInventoryRequest,
  EquipmentCategory,
  CreateEquipmentCategoryRequest,
  UpdateEquipmentCategoryRequest,
  Vendor,
  CreateVendorRequest,
  UpdateVendorRequest,
  Ticket,
  CreateTicketRequest,
  UpdateTicketRequest,
  File,
  CreateFileRequest,
  UpdateFileRequest,
  Role,
  CreateRoleRequest,
  UpdateRoleRequest,
  Lead,
  CreateLeadRequest,
  UpdateLeadRequest,
  Deal,
  CreateDealRequest,
  UpdateDealRequest,
  Quotation,
  CreateQuotationRequest,
  UpdateQuotationRequest,
  CreateQuotationItemRequest,
  UpdateQuotationItemRequest,
  ApiResponse,
  InnerApiResponse,
  PaginatedResponse,
  PaginationQuery
} from '@/types'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://cmsapi.jenexusenergy.com/api/v1'

// API Client class
class ApiClient {
  private axiosInstance: AxiosInstance

  constructor(baseURL: string) {
    this.axiosInstance = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
    })

    // Add response interceptor for consistent error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        console.error('API Error:', error)
        return Promise.reject(error)
      }
    )
  }

  // Set authorization token
  setAuthToken(token: string) {
    this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`
  }

  // Remove authorization token
  removeAuthToken() {
    delete this.axiosInstance.defaults.headers.common['Authorization']
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.request({
        url: endpoint,
        method,
        data,
      })

      const rawData = response.data

      // Check if this is a paginated response (has limit, page/skip, total properties)
      const isPaginatedResponse = typeof rawData.limit === 'number' &&
          (typeof rawData.page === 'number' || typeof rawData.skip === 'number') &&
          typeof rawData.total === 'number'
      
      if (isPaginatedResponse) {
        // Convert skip to page if needed (for backward compatibility)
        let pageNumber = rawData.page
        if (pageNumber === undefined && typeof rawData.skip === 'number') {
          pageNumber = Math.floor(rawData.skip / rawData.limit) + 1
        }
        
        // Normalize null data to empty array for consistency
        const normalizedData = {
          ...rawData,
          data: rawData.data || [],
          page: pageNumber,
          skip: undefined
        }
        return {
          success: true,
          data: {
            success: true,
            data: normalizedData as T,
            timestamp: new Date().toISOString()
          } as InnerApiResponse<T>
        }
      } else if (rawData.success) {
        // This is a standard success response
        return {
          success: true,
          data: {
            success: true,
            data: rawData.data,
            timestamp: new Date().toISOString()
          } as InnerApiResponse<T>
        }
      } else {
        return {
          success: false,
          error: rawData.error || rawData.message || 'API request failed',
        }
      }
    } catch (error) {
      // Handle Axios errors
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError
        if (axiosError.response) {
          // Server responded with error status
          const errorData = axiosError.response.data as any
          return {
            success: false,
            error: errorData?.message || errorData?.error || `HTTP error! status: ${axiosError.response.status}`,
          }
        } else if (axiosError.request) {
          // Request was made but no response received
          return {
            success: false,
            error: 'Network error: No response from server',
          }
        } else {
          // Something else happened
          return {
            success: false,
            error: axiosError.message || 'Request setup error',
          }
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }

  // GET request
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'GET')
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'POST', data)
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'PUT', data)
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'DELETE')
  }

  // Helper method to build query strings
  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    const queryString = searchParams.toString()
    return queryString ? `?${queryString}` : ''
  }

  // GET request with query parameters
  async getWithQuery<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const queryString = params ? this.buildQueryString(params) : ''
    return this.request<T>(`${endpoint}${queryString}`, 'GET')
  }

  // File upload with FormData
  async uploadFile<T>(endpoint: string, formData: FormData): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.request({
        url: endpoint,
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      const rawData = response.data

      // Handle the server response structure
      if (rawData.success !== undefined && rawData.success) {
        // Note: data can be null, which is valid (e.g., when data is not created yet)
        return {
          success: true,
          data: {
            success: true,
            data: rawData.data,
            timestamp: new Date().toISOString()
          }
        }
      } else {
        return {
          success: false,
          error: rawData.error || rawData.message || 'File upload failed',
        }
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const axiosError = error
        if (axiosError.response) {
          const errorData = axiosError.response.data as any
          return {
            success: false,
            error: errorData?.message || errorData?.error || `HTTP error! status: ${axiosError.response.status}`,
          }
        } else if (axiosError.request) {
          return {
            success: false,
            error: 'Network error: No response from server',
          }
        } else {
          return {
            success: false,
            error: axiosError.message || 'Request setup error',
          }
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL)

// User API Service
export class UserService {
  // Get user by ID
  static async getUserById(id: string): Promise<ApiResponse<UserResponse>> {
    return apiClient.get<UserResponse>(`/users/${id}`)
  }

  // Get all users with pagination and search
  static async getUsers(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<UserResponse>>> {
    return apiClient.getWithQuery<PaginatedResponse<UserResponse>>('/users', params)
  }

  // Create new user
  static async createUser(data: CreateUserRequest): Promise<ApiResponse<UserResponse>> {
    return apiClient.post<UserResponse>('/users', data)
  }

  // Update user
  static async updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<UserResponse>> {
    return apiClient.put<UserResponse>(`/users/${id}`, data)
  }

  // Delete user (soft delete)
  static async deleteUser(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/users/${id}`)
  }
}

// Customer API Service
export class CustomerService {
  // Get all customers with pagination and search
  static async getCustomers(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Customer>>> {
    return apiClient.getWithQuery<PaginatedResponse<Customer>>('/customers', params)
  }

  // Get customer by ID
  static async getCustomerById(id: string): Promise<ApiResponse<Customer>> {
    return apiClient.get<Customer>(`/customers/${id}`)
  }

  // Create new customer
  static async createCustomer(data: CreateCustomerRequest): Promise<ApiResponse<Customer>> {
    return apiClient.post<Customer>('/customers', data)
  }

  // Update customer
  static async updateCustomer(id: string, data: UpdateCustomerRequest): Promise<ApiResponse<Customer>> {
    return apiClient.put<Customer>(`/customers/${id}`, data)
  }

  // Delete customer (soft delete)
  static async deleteCustomer(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customers/${id}`)
  }

  // Get customers by parent ID
  static async getCustomersByParent(parentId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Customer>>> {
    const queryParams = { ...params, parentId }
    return apiClient.getWithQuery<PaginatedResponse<Customer>>('/customers', queryParams)
  }
}

// Customer Package API Service
export class CustomerPackageService {
  // Get all customer packages with pagination and search
  static async getCustomerPackages(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<CustomerPackage>>> {
    return apiClient.getWithQuery<PaginatedResponse<CustomerPackage>>('/customer-packages', params)
  }

  // Get customer package by ID
  static async getCustomerPackageById(id: string): Promise<ApiResponse<CustomerPackage>> {
    return apiClient.get<CustomerPackage>(`/customer-packages/${id}`)
  }

  // Get customer packages by customer ID
  static async getCustomerPackagesByCustomerId(customerId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<CustomerPackage>>> {
    return apiClient.getWithQuery<PaginatedResponse<CustomerPackage>>(`/customer-packages/customer/${customerId}`, params)
  }

  // Create new customer package
  static async createCustomerPackage(data: CreateCustomerPackageRequest): Promise<ApiResponse<CustomerPackage>> {
    return apiClient.post<CustomerPackage>('/customer-packages', data)
  }

  // Update customer package
  static async updateCustomerPackage(id: string, data: UpdateCustomerPackageRequest): Promise<ApiResponse<CustomerPackage>> {
    return apiClient.put<CustomerPackage>(`/customer-packages/${id}`, data)
  }

  // Delete customer package (soft delete)
  static async deleteCustomerPackage(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customer-packages/${id}`)
  }
}

// Customer Document API Service
export class CustomerDocumentService {
  // Get all customer documents with pagination and search
  static async getCustomerDocuments(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<CustomerDocument>>> {
    return apiClient.getWithQuery<PaginatedResponse<CustomerDocument>>('/customer-files', params)
  }

  // Get customer document by ID
  static async getCustomerDocumentById(id: string): Promise<ApiResponse<CustomerDocument>> {
    return apiClient.get<CustomerDocument>(`/customer-files/${id}`)
  }

  // Get customer documents by customer ID
  static async getCustomerDocumentsByCustomerId(customerId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<CustomerDocument>>> {
    return apiClient.getWithQuery<PaginatedResponse<CustomerDocument>>(`/customer-files/${customerId}`, params)
  }

  // Create new customer document
  static async createCustomerDocument(data: CreateCustomerDocumentRequest): Promise<ApiResponse<CustomerDocument>> {
    return apiClient.post<CustomerDocument>('/customer-files', data)
  }

  // Update customer document
  static async updateCustomerDocument(id: string, data: UpdateCustomerDocumentRequest): Promise<ApiResponse<CustomerDocument>> {
    return apiClient.put<CustomerDocument>(`/customer-files/${id}`, data)
  }

  // Delete customer document (soft delete)
  static async deleteCustomerDocument(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customer-files/${id}`)
  }
}

// File API Service
export class FileService {
  // Get all files with pagination and search
  static async getFiles(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<File>>> {
    return apiClient.getWithQuery<PaginatedResponse<File>>('/customer-files', params)
  }

  // Get file by ID
  static async getFileById(id: string): Promise<ApiResponse<File>> {
    return apiClient.get<File>(`/customer-files/${id}`)
  }

  // Get files by uploaded user ID
  static async getFilesByUploadedBy(uploadedBy: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<File>>> {
    const queryParams = { ...params, uploadedBy }
    return apiClient.getWithQuery<PaginatedResponse<File>>('/customer-files', queryParams)
  }

  // Create new file (upload)
  static async createFile(data: CreateFileRequest): Promise<ApiResponse<File>> {
    return apiClient.post<File>('/customer-files', data)
  }

  // Update file metadata
  static async updateFile(id: string, data: UpdateFileRequest): Promise<ApiResponse<File>> {
    return apiClient.put<File>(`/customer-files/${id}`, data)
  }

  // Delete file (soft delete)
  static async deleteFile(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customer-files/${id}`)
  }

  // Upload file with FormData (for actual file upload)
  static async uploadFile(formData: FormData): Promise<ApiResponse<File>> {
    return apiClient.uploadFile<File>('/customer-files/upload', formData)
  }

  // Get file download URL
  static async getFileDownloadUrl(id: string): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.get<{ downloadUrl: string }>(`/customer-files/${id}/download-url`)
  }
}

// Customer Services API Service
export class CustomerServicesService {
  // Get all customer services with pagination and search
  static async getCustomerServices(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<CustomerServices>>> {
    return apiClient.getWithQuery<PaginatedResponse<CustomerServices>>('/customer-services', params)
  }

  // Get customer service by ID
  static async getCustomerServiceById(id: string): Promise<ApiResponse<CustomerServices>> {
    return apiClient.get<CustomerServices>(`/customer-services/${id}`)
  }

  // Get customer services by customer ID
  static async getCustomerServicesByCustomerId(customerId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<CustomerServices>>> {
    const queryParams = { ...params, customerId }
    return apiClient.getWithQuery<PaginatedResponse<CustomerServices>>('/customer-services', queryParams)
  }

  // Create new customer service
  static async createCustomerService(data: CreateCustomerServicesRequest): Promise<ApiResponse<CustomerServices>> {
    return apiClient.post<CustomerServices>('/customer-services', data)
  }

  // Update customer service
  static async updateCustomerService(id: string, data: UpdateCustomerServicesRequest): Promise<ApiResponse<CustomerServices>> {
    return apiClient.put<CustomerServices>(`/customer-services/${id}`, data)
  }

  // Delete customer service (soft delete)
  static async deleteCustomerService(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customer-services/${id}`)
  }
}

// Package API Service
export class PackageService {
  // Get all packages with pagination and search
  static async getPackages(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Package>>> {
    return apiClient.getWithQuery<PaginatedResponse<Package>>('/packages', params)
  }

  // Get package by ID
  static async getPackageById(id: string): Promise<ApiResponse<Package>> {
    return apiClient.get<Package>(`/packages/${id}`)
  }

  // Create new package
  static async createPackage(data: CreatePackageRequest): Promise<ApiResponse<Package>> {
    return apiClient.post<Package>('/packages', data)
  }

  // Update package
  static async updatePackage(id: string, data: UpdatePackageRequest): Promise<ApiResponse<Package>> {
    return apiClient.put<Package>(`/packages/${id}`, data)
  }

  // Delete package (soft delete)
  static async deletePackage(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/packages/${id}`)
  }
}

// Equipment API Service
export class EquipmentService {
  // Get all equipment with pagination and search
  static async getEquipment(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Equipment>>> {
    return apiClient.getWithQuery<PaginatedResponse<Equipment>>('/equipment', params)
  }

  // Get equipment by ID
  static async getEquipmentById(id: string): Promise<ApiResponse<Equipment>> {
    return apiClient.get<Equipment>(`/equipment/${id}`)
  }

  // Get equipment by vendor ID
  static async getEquipmentByVendorId(vendorId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Equipment>>> {
    const queryParams = { ...params, vendorId }
    return apiClient.getWithQuery<PaginatedResponse<Equipment>>('/equipment', queryParams)
  }

  // Get equipment by category
  static async getEquipmentByCategory(category0: string, category1?: string, category2?: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Equipment>>> {
    const queryParams = { ...params, category0, category1, category2 }
    return apiClient.getWithQuery<PaginatedResponse<Equipment>>('/equipment', queryParams)
  }

  // Get equipment by order ID
  static async getEquipmentByOrderId(orderId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Equipment>>> {
    const queryParams = { ...params, orderId }
    return apiClient.getWithQuery<PaginatedResponse<Equipment>>('/equipment', queryParams)
  }

  // Create new equipment
  static async createEquipment(data: CreateEquipmentRequest): Promise<ApiResponse<Equipment>> {
    return apiClient.post<Equipment>('/equipment', data)
  }

  // Update equipment
  static async updateEquipment(id: string, data: UpdateEquipmentRequest): Promise<ApiResponse<Equipment>> {
    return apiClient.put<Equipment>(`/equipment/${id}`, data)
  }

  // Delete equipment (soft delete)
  static async deleteEquipment(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/equipment/${id}`)
  }

  // Stock in equipment
  static async stockInEquipment(id: string, stockinBy: string): Promise<ApiResponse<Equipment>> {
    return apiClient.put<Equipment>(`/equipment/${id}/stock-in`, { stockinBy })
  }

  // Stock out equipment
  static async stockOutEquipment(id: string, stockoutBy: string): Promise<ApiResponse<Equipment>> {
    return apiClient.put<Equipment>(`/equipment/${id}/stock-out`, { stockoutBy })
  }
}

// Equipment Inventory API Service
export class EquipmentInventoryService {
  // Get all equipment inventory with pagination and search
  static async getEquipmentInventory(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<EquipmentInventory>>> {
    return apiClient.getWithQuery<PaginatedResponse<EquipmentInventory>>('/equipment-inventory', params)
  }

  // Get equipment inventory by ID
  static async getEquipmentInventoryById(id: string): Promise<ApiResponse<EquipmentInventory>> {
    return apiClient.get<EquipmentInventory>(`/equipment-inventory/${id}`)
  }

  // Get equipment inventory by customer ID
  static async getEquipmentInventoryByCustomerId(customerId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<EquipmentInventory>>> {
    const queryParams = { ...params, customerId }
    return apiClient.getWithQuery<PaginatedResponse<EquipmentInventory>>('/equipment-inventory', queryParams)
  }

  // Get equipment inventory by equipment ID
  static async getEquipmentInventoryByEquipmentId(equipmentId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<EquipmentInventory>>> {
    const queryParams = { ...params, equipmentId }
    return apiClient.getWithQuery<PaginatedResponse<EquipmentInventory>>('/equipment-inventory', queryParams)
  }

  // Create new equipment inventory
  static async createEquipmentInventory(data: CreateEquipmentInventoryRequest): Promise<ApiResponse<EquipmentInventory>> {
    return apiClient.post<EquipmentInventory>('/equipment-inventory', data)
  }

  // Update equipment inventory
  static async updateEquipmentInventory(id: string, data: UpdateEquipmentInventoryRequest): Promise<ApiResponse<EquipmentInventory>> {
    return apiClient.put<EquipmentInventory>(`/equipment-inventory/${id}`, data)
  }

  // Delete equipment inventory (soft delete)
  static async deleteEquipmentInventory(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/equipment-inventory/${id}`)
  }
}

// Equipment Category API Service
export class EquipmentCategoryService {
  // Get all equipment categories with pagination and search
  static async getEquipmentCategories(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<EquipmentCategory>>> {
    return apiClient.getWithQuery<PaginatedResponse<EquipmentCategory>>('/equipment-categories', params)
  }

  // Get equipment category by ID
  static async getEquipmentCategoryById(id: string): Promise<ApiResponse<EquipmentCategory>> {
    return apiClient.get<EquipmentCategory>(`/equipment-categories/${id}`)
  }

  // Get equipment categories by parent ID
  static async getEquipmentCategoriesByParentId(parentId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<EquipmentCategory>>> {
    const queryParams = { ...params, parentId }
    return apiClient.getWithQuery<PaginatedResponse<EquipmentCategory>>('/equipment-categories', queryParams)
  }

  // Get root equipment categories (no parent)
  static async getRootEquipmentCategories(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<EquipmentCategory>>> {
    const queryParams = { ...params, parentId: null }
    return apiClient.getWithQuery<PaginatedResponse<EquipmentCategory>>('/equipment-categories', queryParams)
  }

  // Create new equipment category
  static async createEquipmentCategory(data: CreateEquipmentCategoryRequest): Promise<ApiResponse<EquipmentCategory>> {
    return apiClient.post<EquipmentCategory>('/equipment-categories', data)
  }

  // Update equipment category
  static async updateEquipmentCategory(id: string, data: UpdateEquipmentCategoryRequest): Promise<ApiResponse<EquipmentCategory>> {
    return apiClient.put<EquipmentCategory>(`/equipment-categories/${id}`, data)
  }

  // Delete equipment category (soft delete)
  static async deleteEquipmentCategory(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/equipment-categories/${id}`)
  }
}

// Vendor API Service
export class VendorService {
  // Get all vendors with pagination and search
  static async getVendors(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Vendor>>> {
    return apiClient.getWithQuery<PaginatedResponse<Vendor>>('/vendors', params)
  }

  // Get vendor by ID
  static async getVendorById(id: string): Promise<ApiResponse<Vendor>> {
    return apiClient.get<Vendor>(`/vendors/${id}`)
  }

  // Create new vendor
  static async createVendor(data: CreateVendorRequest): Promise<ApiResponse<Vendor>> {
    return apiClient.post<Vendor>('/vendors', data)
  }

  // Update vendor
  static async updateVendor(id: string, data: UpdateVendorRequest): Promise<ApiResponse<Vendor>> {
    return apiClient.put<Vendor>(`/vendors/${id}`, data)
  }

  // Delete vendor (soft delete)
  static async deleteVendor(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/vendors/${id}`)
  }
}

// Ticket API Service
export class TicketService {
  // Get all tickets with pagination and search
  static async getTickets(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Ticket>>> {
    return apiClient.getWithQuery<PaginatedResponse<Ticket>>('/tickets', params)
  }

  // Get ticket by ID
  static async getTicketById(id: string): Promise<ApiResponse<Ticket>> {
    return apiClient.get<Ticket>(`/tickets/${id}`)
  }

  // Get tickets by customer ID
  static async getTicketsByCustomerId(customerId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Ticket>>> {
    const queryParams = { ...params, customerId }
    return apiClient.getWithQuery<PaginatedResponse<Ticket>>('/tickets', queryParams)
  }

  // Get tickets by customer package ID
  static async getTicketsByCustomerPackageId(customerPackageId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Ticket>>> {
    const queryParams = { ...params, customerPackageId }
    return apiClient.getWithQuery<PaginatedResponse<Ticket>>('/tickets', queryParams)
  }

  // Get tickets by assignee
  static async getTicketsByAssignee(assignee: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Ticket>>> {
    const queryParams = { ...params, assignee }
    return apiClient.getWithQuery<PaginatedResponse<Ticket>>('/tickets', queryParams)
  }

  // Get tickets by department
  static async getTicketsByDepartment(department: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Ticket>>> {
    const queryParams = { ...params, department }
    return apiClient.getWithQuery<PaginatedResponse<Ticket>>('/tickets', queryParams)
  }

  // Create new ticket
  static async createTicket(data: CreateTicketRequest): Promise<ApiResponse<Ticket>> {
    return apiClient.post<Ticket>('/tickets', data)
  }

  // Update ticket
  static async updateTicket(id: string, data: UpdateTicketRequest): Promise<ApiResponse<Ticket>> {
    return apiClient.put<Ticket>(`/tickets/${id}`, data)
  }

  // Delete ticket (soft delete)
  static async deleteTicket(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/tickets/${id}`)
  }

  // Close ticket
  static async closeTicket(id: string): Promise<ApiResponse<Ticket>> {
    return apiClient.put<Ticket>(`/tickets/${id}/close`)
  }

  // Assign ticket
  static async assignTicket(id: string, assignees: string[]): Promise<ApiResponse<Ticket>> {
    return apiClient.put<Ticket>(`/tickets/${id}/assign`, { assignee: assignees })
  }
}

// Role API Service
export class RoleService {
  // Get all roles with pagination and search
  static async getRoles(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Role>>> {
    return apiClient.getWithQuery<PaginatedResponse<Role>>('/roles', params)
  }

  // Get role by ID
  static async getRoleById(id: string): Promise<ApiResponse<Role>> {
    return apiClient.get<Role>(`/roles/${id}`)
  }

  // Create new role
  static async createRole(data: CreateRoleRequest): Promise<ApiResponse<Role>> {
    return apiClient.post<Role>('/roles', data)
  }

  // Update role
  static async updateRole(id: string, data: UpdateRoleRequest): Promise<ApiResponse<Role>> {
    return apiClient.put<Role>(`/roles/${id}`, data)
  }

  // Delete role (soft delete)
  static async deleteRole(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/roles/${id}`)
  }
}

// Lead API Service
export class LeadService {
  // Get all leads with pagination and search
  static async getLeads(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Lead>>> {
    return apiClient.getWithQuery<PaginatedResponse<Lead>>('/leads', params)
  }

  // Get lead by ID
  static async getLeadById(id: string): Promise<ApiResponse<Lead>> {
    return apiClient.get<Lead>(`/leads/${id}`)
  }

  // Get leads by assigned user
  static async getLeadsByAssignedTo(assignedTo: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Lead>>> {
    const queryParams = { ...params, assignedTo }
    return apiClient.getWithQuery<PaginatedResponse<Lead>>('/leads', queryParams)
  }

  // Get leads by source
  static async getLeadsBySource(source: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Lead>>> {
    const queryParams = { ...params, source }
    return apiClient.getWithQuery<PaginatedResponse<Lead>>('/leads', queryParams)
  }

  // Get leads by status
  static async getLeadsByStatus(status: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Lead>>> {
    const queryParams = { ...params, status }
    return apiClient.getWithQuery<PaginatedResponse<Lead>>('/leads', queryParams)
  }

  // Get leads by priority
  static async getLeadsByPriority(priority: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Lead>>> {
    const queryParams = { ...params, priority }
    return apiClient.getWithQuery<PaginatedResponse<Lead>>('/leads', queryParams)
  }

  // Create new lead
  static async createLead(data: CreateLeadRequest): Promise<ApiResponse<Lead>> {
    return apiClient.post<Lead>('/leads', data)
  }

  // Update lead
  static async updateLead(id: string, data: UpdateLeadRequest): Promise<ApiResponse<Lead>> {
    return apiClient.put<Lead>(`/leads/${id}`, data)
  }

  // Delete lead (soft delete)
  static async deleteLead(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/leads/${id}`)
  }

  // Assign lead to sales executive
  static async assignLead(id: string, assignedTo: string, assignedBy: string): Promise<ApiResponse<Lead>> {
    return apiClient.put<Lead>(`/leads/${id}/assign`, {
      assignedTo,
      assignedBy,
      assignedDate: new Date().toISOString()
    })
  }

  // Convert lead to deal
  static async convertToDeal(id: string, dealId: string): Promise<ApiResponse<Lead>> {
    return apiClient.put<Lead>(`/leads/${id}/convert`, {
      convertedToDeal: dealId,
      convertedDate: new Date().toISOString(),
      status: 'closed_won'
    })
  }

  // Update lead status
  static async updateLeadStatus(id: string, status: string): Promise<ApiResponse<Lead>> {
    return apiClient.put<Lead>(`/leads/${id}/status`, { status })
  }

  // Add follow-up date
  static async scheduleFollowUp(id: string, followUpDate: string): Promise<ApiResponse<Lead>> {
    return apiClient.put<Lead>(`/leads/${id}/follow-up`, { nextFollowUp: followUpDate })
  }

  // Update last contact date
  static async updateLastContact(id: string): Promise<ApiResponse<Lead>> {
    return apiClient.put<Lead>(`/leads/${id}/contact`, { lastContactDate: new Date().toISOString() })
  }
}

// Deal API Service
export class DealService {
  // Get all deals with pagination and search
  static async getDeals(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Deal>>> {
    return apiClient.getWithQuery<PaginatedResponse<Deal>>('/deals', params)
  }

  // Get single deal by ID
  static async getDealById(id: string): Promise<ApiResponse<Deal>> {
    return apiClient.get<Deal>(`/deals/${id}`)
  }

  // Create new deal
  static async createDeal(data: CreateDealRequest): Promise<ApiResponse<Deal>> {
    return apiClient.post<Deal>('/deals', data)
  }

  // Update deal
  static async updateDeal(id: string, data: UpdateDealRequest): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}`, data)
  }

  // Delete deal (soft delete)
  static async deleteDeal(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/deals/${id}`)
  }

  // Get deals by status
  static async getDealsByStatus(status: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Deal>>> {
    const queryParams = { ...params, status }
    return apiClient.getWithQuery<PaginatedResponse<Deal>>('/deals', queryParams)
  }

  // Get deals by stage
  static async getDealsByStage(stage: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Deal>>> {
    const queryParams = { ...params, stage }
    return apiClient.getWithQuery<PaginatedResponse<Deal>>('/deals', queryParams)
  }

  // Get deals by priority
  static async getDealsByPriority(priority: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Deal>>> {
    const queryParams = { ...params, priority }
    return apiClient.getWithQuery<PaginatedResponse<Deal>>('/deals', queryParams)
  }

  // Get deals by sales executive
  static async getDealsBySalesExecutive(salesExecutive: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Deal>>> {
    const queryParams = { ...params, salesExecutive }
    return apiClient.getWithQuery<PaginatedResponse<Deal>>('/deals', queryParams)
  }

  // Get deals by customer
  static async getDealsByCustomer(customerId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Deal>>> {
    const queryParams = { ...params, customerId }
    return apiClient.getWithQuery<PaginatedResponse<Deal>>('/deals', queryParams)
  }

  // Assign deal to sales executive
  static async assignDeal(id: string, salesExecutive: string, assignedBy: string): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/assign`, { salesExecutive, assignedBy })
  }

  // Update deal status
  static async updateDealStatus(id: string, status: string): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/status`, { status })
  }

  // Update deal stage
  static async updateDealStage(id: string, stage: string): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/stage`, { stage })
  }

  // Submit deal for approval
  static async submitDeal(id: string): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/submit`, { submittedDate: new Date().toISOString() })
  }

  // Approve deal
  static async approveDeal(id: string): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/approve`, { approvedDate: new Date().toISOString() })
  }

  // Reject deal
  static async rejectDeal(id: string, reason?: string): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/reject`, { reason })
  }

  // Close deal
  static async closeDeal(id: string): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/close`, { actualCloseDate: new Date().toISOString() })
  }

  // Add hardware item to deal
  static async addHardwareItem(id: string, hardwareItem: any): Promise<ApiResponse<Deal>> {
    return apiClient.post<Deal>(`/deals/${id}/hardware`, hardwareItem)
  }

  // Update hardware item
  static async updateHardwareItem(id: string, itemId: string, hardwareItem: any): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/hardware/${itemId}`, hardwareItem)
  }

  // Remove hardware item
  static async removeHardwareItem(id: string, itemId: string): Promise<ApiResponse<Deal>> {
    return apiClient.delete<Deal>(`/deals/${id}/hardware/${itemId}`)
  }

  // Add vendor quote to deal
  static async addVendorQuote(id: string, vendorQuote: any): Promise<ApiResponse<Deal>> {
    return apiClient.post<Deal>(`/deals/${id}/quotes`, vendorQuote)
  }

  // Update vendor quote
  static async updateVendorQuote(id: string, quoteId: string, vendorQuote: any): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/quotes/${quoteId}`, vendorQuote)
  }

  // Select vendor quote
  static async selectVendorQuote(id: string, quoteId: string): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/quotes/${quoteId}/select`)
  }

  // Add sub-contractor to deal
  static async addSubContractor(id: string, subContractor: any): Promise<ApiResponse<Deal>> {
    return apiClient.post<Deal>(`/deals/${id}/subcontractors`, subContractor)
  }

  // Update sub-contractor
  static async updateSubContractor(id: string, contractorId: string, subContractor: any): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/subcontractors/${contractorId}`, subContractor)
  }

  // Remove sub-contractor
  static async removeSubContractor(id: string, contractorId: string): Promise<ApiResponse<Deal>> {
    return apiClient.delete<Deal>(`/deals/${id}/subcontractors/${contractorId}`)
  }

  // Update technical validation
  static async updateTechnicalValidation(id: string, validation: { siteValidated?: boolean; coverageChecked?: boolean; infraValidated?: boolean; technicalNotes?: string }): Promise<ApiResponse<Deal>> {
    return apiClient.put<Deal>(`/deals/${id}/technical`, validation)
  }
}

// Quotation API Service
export class QuotationService {
  // Get all quotations with pagination and search
  static async getQuotations(params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Quotation>>> {
    return apiClient.getWithQuery<PaginatedResponse<Quotation>>('/quotations', params)
  }

  // Get quotation by ID
  static async getQuotationById(id: string): Promise<ApiResponse<Quotation>> {
    return apiClient.get<Quotation>(`/quotations/${id}`)
  }

  // Get quotations by customer ID
  static async getQuotationsByCustomerId(customerId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Quotation>>> {
    const queryParams = { ...params, customerId }
    return apiClient.getWithQuery<PaginatedResponse<Quotation>>('/quotations', queryParams)
  }

  // Get quotations by lead ID
  static async getQuotationsByLeadId(leadId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Quotation>>> {
    const queryParams = { ...params, leadId }
    return apiClient.getWithQuery<PaginatedResponse<Quotation>>('/quotations', queryParams)
  }

  // Get quotations by deal ID
  static async getQuotationsByDealId(dealId: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Quotation>>> {
    const queryParams = { ...params, dealId }
    return apiClient.getWithQuery<PaginatedResponse<Quotation>>('/quotations', queryParams)
  }

  // Get quotations by status
  static async getQuotationsByStatus(status: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Quotation>>> {
    const queryParams = { ...params, status }
    return apiClient.getWithQuery<PaginatedResponse<Quotation>>('/quotations', queryParams)
  }

  // Get quotations by created by (sales executive)
  static async getQuotationsByCreatedBy(createdBy: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Quotation>>> {
    const queryParams = { ...params, createdBy }
    return apiClient.getWithQuery<PaginatedResponse<Quotation>>('/quotations', queryParams)
  }

  // Get quotations by approved by (sales manager)
  static async getQuotationsByApprovedBy(approvedBy: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Quotation>>> {
    const queryParams = { ...params, approvedBy }
    return apiClient.getWithQuery<PaginatedResponse<Quotation>>('/quotations', queryParams)
  }

  // Get quotations by reviewed by (presales engineer)
  static async getQuotationsByReviewedBy(reviewedBy: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Quotation>>> {
    const queryParams = { ...params, reviewedBy }
    return apiClient.getWithQuery<PaginatedResponse<Quotation>>('/quotations', queryParams)
  }

  // Create new quotation
  static async createQuotation(data: CreateQuotationRequest): Promise<ApiResponse<Quotation>> {
    return apiClient.post<Quotation>('/quotations', data)
  }

  // Update quotation
  static async updateQuotation(id: string, data: UpdateQuotationRequest): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}`, data)
  }

  // Delete quotation (soft delete)
  static async deleteQuotation(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/quotations/${id}`)
  }

  // Calculate quotation totals
  static async calculateTotals(id: string): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/calculate`)
  }

  // Validate minimum margin
  static async validateMinimumMargin(id: string, minimumPercentage: number): Promise<ApiResponse<{ isValid: boolean; currentMargin: number }>> {
    return apiClient.post<{ isValid: boolean; currentMargin: number }>(`/quotations/${id}/validate-margin`, { minimumPercentage })
  }

  // Submit quotation for approval
  static async submitQuotation(id: string): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/submit`, { submittedDate: new Date().toISOString() })
  }

  // Approve quotation
  static async approveQuotation(id: string, approvedBy: string): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/approve`, {
      approvedBy,
      approvedDate: new Date().toISOString(),
      status: 'approved'
    })
  }

  // Reject quotation
  static async rejectQuotation(id: string, rejectionReason?: string): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/reject`, {
      rejectionReason,
      status: 'rejected'
    })
  }

  // Send quotation to customer
  static async sendQuotation(id: string): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/send`, {
      sentDate: new Date().toISOString(),
      status: 'sent'
    })
  }

  // Mark quotation as accepted by customer
  static async acceptQuotation(id: string, customerFeedback?: string): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/accept`, {
      responseDate: new Date().toISOString(),
      status: 'accepted',
      customerFeedback
    })
  }

  // Mark quotation as declined by customer
  static async declineQuotation(id: string, rejectionReason?: string): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/decline`, {
      responseDate: new Date().toISOString(),
      status: 'declined',
      rejectionReason
    })
  }

  // Create revision of quotation
  static async createRevision(id: string, revisionData: Partial<CreateQuotationRequest>): Promise<ApiResponse<Quotation>> {
    return apiClient.post<Quotation>(`/quotations/${id}/revise`, revisionData)
  }

  // Update quotation status
  static async updateQuotationStatus(id: string, status: string): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/status`, { status })
  }

  // Add item to quotation
  static async addQuotationItem(id: string, item: CreateQuotationItemRequest): Promise<ApiResponse<Quotation>> {
    return apiClient.post<Quotation>(`/quotations/${id}/items`, item)
  }

  // Update quotation item
  static async updateQuotationItem(id: string, itemNo: number, item: UpdateQuotationItemRequest): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/items/${itemNo}`, item)
  }

  // Remove quotation item
  static async removeQuotationItem(id: string, itemNo: number): Promise<ApiResponse<Quotation>> {
    return apiClient.delete<Quotation>(`/quotations/${id}/items/${itemNo}`)
  }

  // Duplicate quotation
  static async duplicateQuotation(id: string, newQuotationNo: string): Promise<ApiResponse<Quotation>> {
    return apiClient.post<Quotation>(`/quotations/${id}/duplicate`, { quotationNo: newQuotationNo })
  }

  // Get quotation PDF
  static async getQuotationPdf(id: string): Promise<ApiResponse<{ downloadUrl: string }>> {
    return apiClient.get<{ downloadUrl: string }>(`/quotations/${id}/pdf`)
  }

  // Get quotation history (revisions)
  static async getQuotationHistory(id: string, params?: PaginationQuery): Promise<ApiResponse<PaginatedResponse<Quotation>>> {
    return apiClient.getWithQuery<PaginatedResponse<Quotation>>(`/quotations/${id}/history`, params)
  }

  // Assign quotation for review
  static async assignForReview(id: string, reviewedBy: string): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/assign-review`, { reviewedBy })
  }

  // Complete presales review
  static async completePresalesReview(id: string, reviewNotes?: string): Promise<ApiResponse<Quotation>> {
    return apiClient.put<Quotation>(`/quotations/${id}/complete-review`, {
      reviewNotes,
      reviewedDate: new Date().toISOString()
    })
  }
}

// Auth Service
export class AuthService {
  // Login
  static async login(credentials: { login: string; password: string }): Promise<ApiResponse<{ token: string; user: UserResponse }>> {
    return apiClient.post<{ token: string; user: UserResponse }>('/auth/login', credentials)
  }

  // Logout
  static async logout(): Promise<ApiResponse<void>> {
    const result = await apiClient.post<void>('/auth/logout')
    apiClient.removeAuthToken()
    return result
  }

  // Get current user profile
  static async getCurrentUser(): Promise<ApiResponse<UserResponse>> {
    return apiClient.get<UserResponse>('/auth/profile')
  }

  // Set token for authenticated requests
  static setToken(token: string) {
    apiClient.setAuthToken(token)
  }

  // Remove token
  static removeToken() {
    apiClient.removeAuthToken()
  }
}

// Export the API client for direct use if needed
export { apiClient as default }
