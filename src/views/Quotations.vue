<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Quotations</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Quotation
      </button>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
          <input
            id="search"
            v-model="searchQuery"
            type="text"
            placeholder="Search quotations..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            id="status"
            v-model="statusFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Statuses</option>
            <option value="draft">Draft</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="sent">Sent</option>
            <option value="accepted">Accepted</option>
            <option value="declined">Declined</option>
            <option value="expired">Expired</option>
            <option value="revision">Revision</option>
          </select>
        </div>
        <div>
          <label for="customer" class="block text-sm font-medium text-gray-700 mb-1">Customer</label>
          <select
            id="customer"
            v-model="customerFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Customers</option>
            <!-- TODO: Load customers dynamically -->
          </select>
        </div>
        <div>
          <label for="createdBy" class="block text-sm font-medium text-gray-700 mb-1">Created By</label>
          <select
            id="createdBy"
            v-model="createdByFilter"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Users</option>
            <!-- TODO: Load users dynamically -->
          </select>
        </div>
      </div>
    </div>

    <!-- Quotations Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-900">
          All Quotations ({{ pagination.state.total }})
        </h3>
      </div>

      <div v-if="quotations.length === 0" class="p-6 text-center text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="mt-2">No quotations found</p>
        <p class="text-sm text-gray-400">Get started by adding your first quotation</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Margin</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valid Until</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="quotation in quotations" :key="quotation.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ quotation.quotationNo }}</div>
                  <div class="text-sm text-gray-500">{{ quotation.title }}</div>
                  <div class="text-xs text-gray-400">v{{ quotation.version }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getCustomerName(quotation.customerId) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusBadgeClass(quotation.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ formatStatus(quotation.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  {{ quotation.currency }} {{ formatCurrency(quotation.totalAmount) }}
                </div>
                <div class="text-xs text-gray-500">
                  Subtotal: {{ quotation.currency }} {{ formatCurrency(quotation.subTotal) }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  {{ quotation.currency }} {{ formatCurrency(quotation.grossMargin) }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ quotation.grossMarginPercentage.toFixed(1) }}%
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  {{ quotation.validUntil ? formatDate(quotation.validUntil) : '-' }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  @click="editQuotation(quotation)"
                  class="text-blue-600 hover:text-blue-900 transition-colors"
                >
                  Edit
                </button>
                <button
                  @click="duplicateQuotation(quotation)"
                  class="text-green-600 hover:text-green-900 transition-colors"
                >
                  Duplicate
                </button>
                <button
                  @click="approveQuotation(quotation)"
                  v-if="quotation.status === 'pending'"
                  class="text-purple-600 hover:text-purple-900 transition-colors"
                >
                  Approve
                </button>
                <button
                  @click="sendQuotation(quotation)"
                  v-if="quotation.status === 'approved'"
                  class="text-indigo-600 hover:text-indigo-900 transition-colors"
                >
                  Send
                </button>
                <button
                  @click="createRevision(quotation)"
                  v-if="['sent', 'declined', 'expired'].includes(quotation.status)"
                  class="text-orange-600 hover:text-orange-900 transition-colors"
                >
                  Revise
                </button>
                <button
                  @click="downloadPdf(quotation)"
                  class="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  PDF
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="px-6 py-4 border-t border-gray-200">
        <Pagination
          :current-page="pagination.state.currentPage"
          :total-pages="Math.ceil(pagination.state.total / pagination.state.pageSize)"
          :page-size="pagination.state.pageSize"
          :total="pagination.state.total"
          @page-change="pagination.goToPage"
          @page-size-change="pagination.changePageSize"
        />
      </div>
    </div>

    <!-- Quotation Modal (Add/Edit) -->
    <QuotationModal
      v-if="showAddModal"
      @close="showAddModal = false"
      @success="handleQuotationSuccess"
    />
    <QuotationModal
      v-if="showEditModal && selectedQuotation"
      :quotation="selectedQuotation"
      @close="showEditModal = false"
      @success="handleQuotationSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { QuotationService } from '@/services/api'
import type { Quotation, QuotationStatus } from '@/types'
import QuotationModal from '@/components/modals/QuotationModal.vue'
import Pagination from '@/components/Pagination.vue'
import { usePagination } from '@/composables/usePagination'

// Reactive data
const quotations = ref<Quotation[]>([])
const searchQuery = ref('')
const statusFilter = ref('')
const customerFilter = ref('')
const createdByFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const selectedQuotation = ref<Quotation | undefined>(undefined)

// Pagination
const pagination = usePagination()

// Fetch quotations
const fetchQuotations = async () => {
  try {
    const params = {
      limit: pagination.state.pageSize,
      page: pagination.state.currentPage,
      search: searchQuery.value || undefined,
      status: statusFilter.value || undefined,
      customerId: customerFilter.value || undefined,
      createdBy: createdByFilter.value || undefined
    }

    const response = await QuotationService.getQuotations(params)
    if (response.success && response.data && response.data.success) {
      quotations.value = response.data.data.data || []
      pagination.updateTotal(response.data.data.total || 0)
    } else {
      console.error('Failed to fetch quotations:', response.error)
      quotations.value = []
    }
  } catch (error) {
    console.error('Error fetching quotations:', error)
    quotations.value = []
  }
}

// Watch for filter changes
watch([searchQuery, statusFilter, customerFilter, createdByFilter], () => {
  pagination.goToPage(1)
  fetchQuotations()
}, { debounce: 300 })

// Watch for pagination changes
watch(() => pagination.state.currentPage, fetchQuotations)
watch(() => pagination.state.pageSize, fetchQuotations)

// Helper functions
const getCustomerName = (customerId: string): string => {
  // TODO: Implement customer lookup
  return `Customer ${customerId.slice(-4)}`
}

const getStatusBadgeClass = (status: QuotationStatus): string => {
  const classes = {
    draft: 'bg-gray-100 text-gray-800',
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    sent: 'bg-blue-100 text-blue-800',
    accepted: 'bg-emerald-100 text-emerald-800',
    declined: 'bg-red-100 text-red-800',
    expired: 'bg-gray-100 text-gray-800',
    revision: 'bg-orange-100 text-orange-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const formatStatus = (status: string): string => {
  return status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')
}

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString()
}

// Action handlers
const editQuotation = (quotation: Quotation) => {
  selectedQuotation.value = quotation
  showEditModal.value = true
}

const duplicateQuotation = async (quotation: Quotation) => {
  const newQuotationNo = prompt('Enter new quotation number:')
  if (newQuotationNo) {
    try {
      const response = await QuotationService.duplicateQuotation(quotation.id, newQuotationNo)
      if (response.success && response.data && response.data.success) {
        await fetchQuotations()
        alert('Quotation duplicated successfully!')
      } else {
        alert('Failed to duplicate quotation: ' + (response.error || 'Unknown error'))
      }
    } catch (error) {
      alert('Failed to duplicate quotation: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }
}

const approveQuotation = async (quotation: Quotation) => {
  if (confirm(`Are you sure you want to approve quotation ${quotation.quotationNo}?`)) {
    try {
      // TODO: Get current user ID
      const response = await QuotationService.approveQuotation(quotation.id, 'current-user-id')
      if (response.success && response.data && response.data.success) {
        await fetchQuotations()
        alert('Quotation approved successfully!')
      } else {
        alert('Failed to approve quotation: ' + (response.error || 'Unknown error'))
      }
    } catch (error) {
      alert('Failed to approve quotation: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }
}

const sendQuotation = async (quotation: Quotation) => {
  if (confirm(`Are you sure you want to send quotation ${quotation.quotationNo} to the customer?`)) {
    try {
      const response = await QuotationService.sendQuotation(quotation.id)
      if (response.success && response.data && response.data.success) {
        await fetchQuotations()
        alert('Quotation sent successfully!')
      } else {
        alert('Failed to send quotation: ' + (response.error || 'Unknown error'))
      }
    } catch (error) {
      alert('Failed to send quotation: ' + (error instanceof Error ? error.message : 'Unknown error'))
    }
  }
}

const createRevision = async (quotation: Quotation) => {
  // TODO: Open revision modal with quotation data
  alert('Revision functionality will be implemented')
}

const downloadPdf = async (quotation: Quotation) => {
  try {
    const response = await QuotationService.getQuotationPdf(quotation.id)
    if (response.success && response.data && response.data.success) {
      window.open(response.data.data.downloadUrl, '_blank')
    } else {
      alert('Failed to generate PDF: ' + (response.error || 'Unknown error'))
    }
  } catch (error) {
    alert('Failed to generate PDF: ' + (error instanceof Error ? error.message : 'Unknown error'))
  }
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  selectedQuotation.value = undefined
}

const handleQuotationSuccess = () => {
  fetchQuotations()
  closeModal()
}

// Initialize
onMounted(() => {
  fetchQuotations()
})
</script>
