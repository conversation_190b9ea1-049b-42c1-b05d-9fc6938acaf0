<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Leads</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Lead
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 md:p-6 lg:p-8 rounded-lg shadow">
      <div class="flex flex-col lg:flex-row items-stretch lg:items-center gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search leads..."
            @keyup.enter="handleSearch"
            class="w-full px-3 md:px-4 lg:px-5 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div class="flex flex-col sm:flex-row gap-2">
          <select
            v-model="statusFilter"
            @change="handleFilterChange"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Status</option>
            <option value="new">New</option>
            <option value="contacted">Contacted</option>
            <option value="qualified">Qualified</option>
            <option value="proposal">Proposal</option>
            <option value="negotiation">Negotiation</option>
            <option value="closed_won">Closed Won</option>
            <option value="closed_lost">Closed Lost</option>
            <option value="nurturing">Nurturing</option>
          </select>
          <select
            v-model="priorityFilter"
            @change="handleFilterChange"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Priority</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
          <button
            @click="clearFilters"
            class="px-4 md:px-6 lg:px-8 py-2 md:py-3 lg:py-4 text-sm md:text-base lg:text-lg font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Clear
          </button>
        </div>
      </div>
    </div>

    <!-- Leads Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-900">
          All Leads ({{ pagination.state.total }})
        </h3>
      </div>

      <div v-if="leads.length === 0" class="p-6 text-center text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="mt-2">No leads found</p>
        <p class="text-sm text-gray-400">Get started by adding your first lead</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Lead
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Priority
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Value
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="(lead, index) in leads"
              :key="index"
              class="hover:bg-gray-50 transition-colors"
            >
              <!-- Lead Info -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center">
                    <span class="text-sm font-medium text-white">
                      {{ lead.companyName?.charAt(0)?.toUpperCase() || '?' }}
                    </span>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ lead.companyName || 'Unknown Company' }}</div>
                    <div class="text-sm text-gray-500">{{ lead.leadNo || '-' }}</div>
                  </div>
                </div>
              </td>

              <!-- Contact Info -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ lead.contactPerson || 'No contact person' }}</div>
                <div class="text-sm text-gray-900">{{ lead.contactPhone || 'No phone' }}</div>
                <div class="text-sm text-gray-500">{{ lead.contactEmail || 'No email' }}</div>
              </td>

              <!-- Status -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(lead.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ formatStatus(lead.status) }}
                </span>
              </td>

              <!-- Priority -->
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getPriorityClass(lead.priority)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ formatPriority(lead.priority) }}
                </span>
              </td>

              <!-- Value -->
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ formatCurrency(lead.estimatedValue, lead.currency) }}</div>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-1">
                  <button
                    @click="editLead(lead)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded hover:bg-indigo-50"
                    title="Edit Lead"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="convertLead(lead)"
                    class="text-green-600 hover:text-green-900 transition-colors p-1 rounded hover:bg-green-50 hidden"
                    title="Convert to Deal"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteLead(lead)"
                    class="text-red-600 hover:text-red-900 transition-colors p-1 rounded hover:bg-red-50 hidden"
                    title="Delete Lead"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <Pagination
        v-if="leads.length > 0"
        :current-page="pagination.state.currentPage"
        :page-size="pagination.state.pageSize"
        :total="pagination.state.total"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- Lead Modal (Add/Edit) -->
    <LeadModal
      v-if="showAddModal"
      @close="showAddModal = false"
      @success="handleLeadSuccess"
    />
    <LeadModal
      v-if="showEditModal && selectedLead"
      :lead="selectedLead"
      @close="showEditModal = false"
      @success="handleLeadSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { LeadService } from '@/services/api'
import type { Lead } from '@/types'
import LeadModal from '@/components/modals/LeadModal.vue'
import Pagination from '@/components/Pagination.vue'
import { usePagination } from '@/composables/usePagination'

// Reactive data
const leads = ref<Lead[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const searchQuery = ref('')
const statusFilter = ref('')
const priorityFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const selectedLead = ref<Lead | null>(null)

// Pagination setup
const pagination = usePagination({
  initialPageSize: 10,
  onPageChange: async () => {
    await loadLeads()
  }
})

// Watch for search query changes with debounce
let searchTimeout: number | null = null
watch(searchQuery, async () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(async () => {
    pagination.reset()
    await loadLeads()
  }, 300)
})

const handleSearch = async () => {
  pagination.reset()
  await loadLeads()
}

const handleFilterChange = async () => {
  pagination.reset()
  await loadLeads()
}

const clearFilters = async () => {
  searchQuery.value = ''
  statusFilter.value = ''
  priorityFilter.value = ''
  pagination.reset()
  await loadLeads()
}

// Pagination handlers
const handlePageChange = async (page: number) => {
  await pagination.goToPage(page)
}

const handlePageSizeChange = async (pageSize: number) => {
  await pagination.changePageSize(pageSize)
}

const handleLeadSuccess = () => {
  // Reload leads after successful add/edit
  loadLeads()
}

const editLead = (lead: Lead) => {
  selectedLead.value = lead
  showEditModal.value = true
}

const convertLead = async (lead: Lead) => {
  if (confirm(`Are you sure you want to convert ${lead.companyName || 'this lead'} to a deal?`)) {
    try {
      // For now, we'll just update the status to closed_won
      // In a real implementation, you'd create a deal and link it
      const response = await LeadService.updateLeadStatus(lead.id, 'closed_won')
      if (response.success && response.data && response.data.success) {
        await loadLeads() // Reload to show updated status
      } else {
        alert('Failed to convert lead: ' + (response.error || 'Unknown error'))
      }
    } catch (err) {
      alert('Failed to convert lead: ' + (err instanceof Error ? err.message : 'Unknown error'))
    }
  }
}

const deleteLead = async (lead: Lead) => {
  if (confirm(`Are you sure you want to delete ${lead.companyName || 'this lead'}?`)) {
    try {
      const response = await LeadService.deleteLead(lead.id)
      if (response.success && response.data && response.data.success) {
        // Remove from local array
        leads.value = leads.value.filter(l => l.id !== lead.id)
      } else {
        alert('Failed to delete lead: ' + (response.error || 'Unknown error'))
      }
    } catch (err) {
      alert('Failed to delete lead: ' + (err instanceof Error ? err.message : 'Unknown error'))
    }
  }
}

const loadLeads = async () => {
  loading.value = true
  error.value = null

  try {
    const params = pagination.getQueryParamsWithSearch(searchQuery.value)

    // Add filters to params
    const queryParams: any = { ...params }
    if (statusFilter.value) {
      queryParams.status = statusFilter.value
    }
    if (priorityFilter.value) {
      queryParams.priority = priorityFilter.value
    }

    const response = await LeadService.getLeads(queryParams)

    if (response.success && response.data?.success && response.data.data) {
      const paginatedData = response.data.data
      leads.value = paginatedData.data
      pagination.updateFromResponse(paginatedData)
    } else {
      error.value = response.error || 'Failed to load leads'
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load leads'
  } finally {
    loading.value = false
  }
}

// Utility functions
const getStatusClass = (status: string): string => {
  switch (status) {
    case 'new': return 'bg-blue-100 text-blue-800'
    case 'contacted': return 'bg-yellow-100 text-yellow-800'
    case 'qualified': return 'bg-purple-100 text-purple-800'
    case 'proposal': return 'bg-indigo-100 text-indigo-800'
    case 'negotiation': return 'bg-orange-100 text-orange-800'
    case 'closed_won': return 'bg-green-100 text-green-800'
    case 'closed_lost': return 'bg-red-100 text-red-800'
    case 'nurturing': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getPriorityClass = (priority: string): string => {
  switch (priority) {
    case 'low': return 'bg-gray-100 text-gray-800'
    case 'medium': return 'bg-yellow-100 text-yellow-800'
    case 'high': return 'bg-orange-100 text-orange-800'
    case 'urgent': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatStatus = (status: string): string => {
  return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatPriority = (priority: string): string => {
  return priority.charAt(0).toUpperCase() + priority.slice(1)
}

const formatCurrency = (value: number, currency: string): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency || 'USD'
  }).format(value || 0)
}

onMounted(() => {
  loadLeads()
})
</script>
