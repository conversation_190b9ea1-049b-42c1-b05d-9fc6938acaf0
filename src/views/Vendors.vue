<template>
  <div class="p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Vendors</h1>
      <p class="mt-1 text-sm text-gray-600">Manage vendor information</p>
    </div>

    <!-- Actions Bar -->
    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <!-- Search -->
      <div class="flex-1 max-w-md">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            v-model="searchQuery"
            type="text"
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search vendors..."
          />
        </div>
      </div>

      <!-- Add Vendor Button -->
      <button
        @click="openAddModal"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        Add Vendor
      </button>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <div class="flex">
        <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <div class="ml-3">
          <p class="text-sm text-red-800">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Vendors Table -->
    <div v-else class="bg-white shadow overflow-hidden sm:rounded-md">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Info</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="vendor in vendors" :key="vendor.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ vendor.name }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ vendor.contactPerson || '-' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  <div v-if="vendor.contactNo">{{ vendor.contactNo }}</div>
                  <div v-if="vendor.email" class="text-gray-500">{{ vendor.email }}</div>
                  <div v-if="!vendor.contactNo && !vendor.email">-</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button
                    @click="editVendor(vendor)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors"
                    title="Edit Vendor"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteVendor(vendor)"
                    class="text-red-600 hover:text-red-900 transition-colors hidden"
                    title="Delete Vendor"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <Pagination
        v-if="vendors.length > 0"
        :current-page="pagination.state.currentPage"
        :page-size="pagination.state.pageSize"
        :total="pagination.state.total"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />

      <!-- Empty State -->
      <div v-if="vendors.length === 0" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No vendors found</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating a new vendor.</p>
        <div class="mt-6">
          <button
            @click="openAddModal"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Vendor
          </button>
        </div>
      </div>
    </div>

    <!-- Modal -->
    <VendorModal
      v-if="showModal"
      :vendor="selectedVendor"
      @close="closeModal"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { VendorService } from '@/services/api'
import type { Vendor } from '@/types'
import VendorModal from '@/components/modals/VendorModal.vue'
import Pagination from '@/components/Pagination.vue'
import { usePagination } from '@/composables/usePagination'

// Reactive data
const vendors = ref<Vendor[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const searchQuery = ref('')

// Modal states
const showModal = ref(false)
const selectedVendor = ref<Vendor | undefined>(undefined)

// Pagination setup
const pagination = usePagination({
  initialPageSize: 10,
  onPageChange: async () => {
    await loadVendors()
  }
})

// Watch for search query changes with debounce
let searchTimeout: number | null = null
watch(searchQuery, async () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(async () => {
    pagination.reset()
    await loadVendors()
  }, 300)
})

// Pagination handlers
const handlePageChange = async (page: number) => {
  await pagination.goToPage(page)
}

const handlePageSizeChange = async (pageSize: number) => {
  await pagination.changePageSize(pageSize)
}

// Computed properties
const filteredVendors = computed(() => {
  // Since we're using server-side pagination, we don't need client-side filtering
  // The filtering is now handled by the API
  return vendors.value
})

// Methods
const loadVendors = async () => {
  loading.value = true
  error.value = null

  try {
    const params = pagination.getQueryParamsWithSearch(searchQuery.value)
    const response = await VendorService.getVendors(params)
    if (response.success && response.data?.success && response.data.data) {
      const paginatedVendors = response.data.data
      vendors.value = paginatedVendors.data
      pagination.updateFromResponse(paginatedVendors)
    } else {
      error.value = response.error || 'Failed to load vendors'
    }
  } catch (err) {
    error.value = 'Failed to load vendors'
    console.error('Load vendors error:', err)
  } finally {
    loading.value = false
  }
}

const editVendor = (vendor: Vendor) => {
  selectedVendor.value = vendor
  showModal.value = true
}

const openAddModal = () => {
  selectedVendor.value = undefined
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  selectedVendor.value = undefined
}

const deleteVendor = async (vendor: Vendor) => {
  if (!confirm(`Are you sure you want to delete the vendor "${vendor.name}"?`)) {
    return
  }

  try {
    const response = await VendorService.deleteVendor(vendor.id)
    if (response.success) {
      await loadVendors()
    } else {
      error.value = response.error || 'Failed to delete vendor'
    }
  } catch (err) {
    error.value = 'Failed to delete vendor'
    console.error('Delete vendor error:', err)
  }
}

const handleSuccess = () => {
  closeModal()
  loadVendors()
}

// Load vendors on component mount
onMounted(() => {
  loadVendors()
})
</script>
