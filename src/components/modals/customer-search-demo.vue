<template>
  <div class="p-6 max-w-md mx-auto bg-white rounded-lg shadow-lg">
    <h2 class="text-xl font-semibold mb-4">Customer Search Demo</h2>
    
    <div class="mb-4">
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Search Customers (Limit 10)
      </label>
      <SearchableDropdown
        v-model="selectedCustomerId"
        :options="customers"
        label-key="name"
        value-key="id"
        placeholder="Type to search customers..."
        :search-function="searchCustomers"
        @change="onCustomerChange"
      />
    </div>

    <div v-if="selectedCustomer" class="mt-4 p-4 bg-gray-50 rounded-lg">
      <h3 class="font-medium text-gray-900 mb-2">Selected Customer:</h3>
      <div class="text-sm text-gray-600">
        <p><strong>Name:</strong> {{ selectedCustomer.name }}</p>
        <p><strong>Contact:</strong> {{ selectedCustomer.contactPerson }}</p>
        <p><strong>Email:</strong> {{ selectedCustomer.email }}</p>
        <p><strong>Phone:</strong> {{ selectedCustomer.contactNo }}</p>
      </div>
    </div>

    <div class="mt-4 text-xs text-gray-500">
      <p>This demo shows the SearchableDropdown component fetching real customers from the API with a limit of 10 results.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { CustomerService } from '@/services/api'
import type { Customer } from '@/types'
import SearchableDropdown from '@/components/SearchableDropdown.vue'

// Reactive data
const customers = ref<Customer[]>([])
const selectedCustomerId = ref<string | null>(null)

// Computed
const selectedCustomer = computed(() => {
  return customers.value.find(c => c.id === selectedCustomerId.value) || null
})

// Customer search function
const searchCustomers = async (query: string) => {
  try {
    const response = await CustomerService.getCustomers({
      search: query,
      limit: 10
    })
    
    if (response.success && response.data && response.data.data) {
      customers.value = response.data.data.data || []
    }
  } catch (error) {
    console.error('Error searching customers:', error)
    customers.value = []
  }
}

// Customer change handler
const onCustomerChange = (option: any) => {
  const customer = option as Customer | null
  if (customer) {
    selectedCustomerId.value = customer.id
  } else {
    selectedCustomerId.value = null
  }
}

// Load initial customers on mount
const loadInitialCustomers = async () => {
  try {
    const response = await CustomerService.getCustomers({
      limit: 10
    })
    
    if (response.success && response.data && response.data.data) {
      customers.value = response.data.data.data || []
    }
  } catch (error) {
    console.error('Error loading customers:', error)
    customers.value = []
  }
}

// Initialize
loadInitialCustomers()
</script>
