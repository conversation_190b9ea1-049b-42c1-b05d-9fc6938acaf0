<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Quotation' : 'Add New Quotation' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Basic Quotation Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Basic Quotation Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="quotationNo" class="block text-sm font-medium text-gray-700 mb-1">Quotation Number *</label>
                <input
                  id="quotationNo"
                  v-model="form.quotationNo"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter quotation number"
                />
              </div>
              <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
                <input
                  id="title"
                  v-model="form.title"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter quotation title"
                />
              </div>
              <div>
                <label for="version" class="block text-sm font-medium text-gray-700 mb-1">Version</label>
                <input
                  id="version"
                  v-model.number="form.version"
                  type="number"
                  min="1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div class="mt-4">
              <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                id="description"
                v-model="form.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter quotation description"
              ></textarea>
            </div>
          </div>

          <!-- Related Records -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Related Records</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="customerId" class="block text-sm font-medium text-gray-700 mb-1">Customer *</label>
                <SearchableDropdown
                  id="customerId"
                  v-model="form.customerId"
                  :options="customers"
                  label-key="name"
                  value-key="id"
                  placeholder="Search customers..."
                  :required="true"
                  :search-function="searchCustomers"
                  @change="onCustomerChange"
                />
              </div>
              <div>
                <label for="leadId" class="block text-sm font-medium text-gray-700 mb-1">Related Lead</label>
                <select
                  id="leadId"
                  v-model="form.leadId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select lead</option>
                  <!-- TODO: Load leads dynamically -->
                  <option value="lead-1">Lead 1</option>
                  <option value="lead-2">Lead 2</option>
                </select>
              </div>
              <div>
                <label for="dealId" class="block text-sm font-medium text-gray-700 mb-1">Related Deal</label>
                <select
                  id="dealId"
                  v-model="form.dealId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select deal</option>
                  <!-- TODO: Load deals dynamically -->
                  <option value="deal-1">Deal 1</option>
                  <option value="deal-2">Deal 2</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Quotation Classification -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Quotation Classification</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                <select
                  id="status"
                  v-model="form.status"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select status</option>
                  <option value="draft">Draft</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="sent">Sent</option>
                  <option value="accepted">Accepted</option>
                  <option value="declined">Declined</option>
                  <option value="expired">Expired</option>
                  <option value="revision">Revision</option>
                </select>
              </div>
              <div>
                <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">Currency *</label>
                <select
                  id="currency"
                  v-model="form.currency"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select currency</option>
                  <option value="MYR">MYR</option>
                  <option value="USD">USD</option>
                  <option value="SGD">SGD</option>
                </select>
              </div>
              <div>
                <label for="validUntil" class="block text-sm font-medium text-gray-700 mb-1">Valid Until</label>
                <input
                  id="validUntil"
                  v-model="form.validUntil"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          <!-- Financial Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Financial Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label for="subTotal" class="block text-sm font-medium text-gray-700 mb-1">Subtotal</label>
                <input
                  id="subTotal"
                  v-model.number="form.subTotal"
                  type="number"
                  step="0.01"
                  min="0"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="taxPercentage" class="block text-sm font-medium text-gray-700 mb-1">Tax (%)</label>
                <input
                  id="taxPercentage"
                  v-model.number="form.taxPercentage"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="discountPercentage" class="block text-sm font-medium text-gray-700 mb-1">Discount (%)</label>
                <input
                  id="discountPercentage"
                  v-model.number="form.discountPercentage"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="totalAmount" class="block text-sm font-medium text-gray-700 mb-1">Total Amount</label>
                <input
                  id="totalAmount"
                  v-model.number="form.totalAmount"
                  type="number"
                  step="0.01"
                  min="0"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          <!-- Assignment -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Assignment</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="createdBy" class="block text-sm font-medium text-gray-700 mb-1">Created By *</label>
                <select
                  id="createdBy"
                  v-model="form.createdBy"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select user</option>
                  <!-- TODO: Load users dynamically -->
                  <option value="user-1">Sales Executive 1</option>
                  <option value="user-2">Sales Executive 2</option>
                </select>
              </div>
              <div>
                <label for="approvedBy" class="block text-sm font-medium text-gray-700 mb-1">Approved By</label>
                <select
                  id="approvedBy"
                  v-model="form.approvedBy"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select manager</option>
                  <!-- TODO: Load managers dynamically -->
                  <option value="manager-1">Sales Manager 1</option>
                  <option value="manager-2">Sales Manager 2</option>
                </select>
              </div>
              <div>
                <label for="reviewedBy" class="block text-sm font-medium text-gray-700 mb-1">Reviewed By</label>
                <select
                  id="reviewedBy"
                  v-model="form.reviewedBy"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select engineer</option>
                  <!-- TODO: Load presales engineers dynamically -->
                  <option value="engineer-1">Presales Engineer 1</option>
                  <option value="engineer-2">Presales Engineer 2</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Quotation Items -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-md font-medium text-gray-900">Quotation Items</h4>
              <button
                type="button"
                @click="addItem"
                class="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 transition-colors text-sm"
              >
                Add Item
              </button>
            </div>

            <div v-if="form.items.length === 0" class="text-center py-8 text-gray-500">
              <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p class="mt-2">No items added yet</p>
              <p class="text-sm text-gray-400">Click "Add Item" to get started</p>
            </div>

            <div v-else class="space-y-4">
              <div
                v-for="(item, index) in form.items"
                :key="index"
                class="bg-white p-4 rounded-lg border border-gray-200"
              >
                <div class="flex items-center justify-between mb-3">
                  <h5 class="text-sm font-medium text-gray-900">Item {{ index + 1 }}</h5>
                  <button
                    type="button"
                    @click="removeItem(index)"
                    class="text-red-600 hover:text-red-800 transition-colors"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Service Type *</label>
                    <select
                      v-model="item.serviceType"
                      required
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      <option value="">Select service</option>
                      <option value="internet">Internet</option>
                      <option value="networking">Networking</option>
                      <option value="security">Security</option>
                      <option value="cloud">Cloud</option>
                      <option value="maintenance">Maintenance</option>
                      <option value="consultation">Consultation</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Category *</label>
                    <input
                      v-model="item.category"
                      type="text"
                      required
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="Enter category"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Name *</label>
                    <input
                      v-model="item.name"
                      type="text"
                      required
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="Enter item name"
                    />
                  </div>
                </div>

                <div class="mt-3">
                  <label class="block text-xs font-medium text-gray-700 mb-1">Description *</label>
                  <textarea
                    v-model="item.description"
                    required
                    rows="2"
                    class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Enter item description"
                  ></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-4 gap-3 mt-3">
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Quantity *</label>
                    <input
                      v-model.number="item.quantity"
                      type="number"
                      min="1"
                      required
                      @input="calculateItemTotals(item)"
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Unit</label>
                    <input
                      v-model="item.unit"
                      type="text"
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="e.g., pcs, months"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Unit Price *</label>
                    <input
                      v-model.number="item.unitPrice"
                      type="number"
                      step="0.01"
                      min="0"
                      required
                      @input="calculateItemTotals(item)"
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Total Price</label>
                    <input
                      v-model.number="item.totalPrice"
                      type="number"
                      step="0.01"
                      readonly
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md bg-gray-50 focus:outline-none"
                    />
                  </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mt-3">
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Unit Cost</label>
                    <input
                      v-model.number="item.unitCost"
                      type="number"
                      step="0.01"
                      min="0"
                      @input="calculateItemTotals(item)"
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Total Cost</label>
                    <input
                      v-model.number="item.totalCost"
                      type="number"
                      step="0.01"
                      readonly
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md bg-gray-50 focus:outline-none"
                    />
                  </div>
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Margin %</label>
                    <input
                      v-model.number="item.marginPercentage"
                      type="number"
                      step="0.01"
                      readonly
                      class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md bg-gray-50 focus:outline-none"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Terms and Conditions -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Terms and Conditions</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="paymentTerms" class="block text-sm font-medium text-gray-700 mb-1">Payment Terms</label>
                <textarea
                  id="paymentTerms"
                  v-model="form.paymentTerms"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter payment terms"
                ></textarea>
              </div>
              <div>
                <label for="deliveryTerms" class="block text-sm font-medium text-gray-700 mb-1">Delivery Terms</label>
                <textarea
                  id="deliveryTerms"
                  v-model="form.deliveryTerms"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter delivery terms"
                ></textarea>
              </div>
              <div>
                <label for="warrantyTerms" class="block text-sm font-medium text-gray-700 mb-1">Warranty Terms</label>
                <textarea
                  id="warrantyTerms"
                  v-model="form.warrantyTerms"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter warranty terms"
                ></textarea>
              </div>
              <div>
                <label for="specialTerms" class="block text-sm font-medium text-gray-700 mb-1">Special Terms</label>
                <textarea
                  id="specialTerms"
                  v-model="form.specialTerms"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter special terms"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Notes -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Notes</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Public Notes</label>
                <textarea
                  id="notes"
                  v-model="form.notes"
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter public notes (visible to customer)"
                ></textarea>
              </div>
              <div>
                <label for="internalNotes" class="block text-sm font-medium text-gray-700 mb-1">Internal Notes</label>
                <textarea
                  id="internalNotes"
                  v-model="form.internalNotes"
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter internal notes (not visible to customer)"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
            >
              {{ isEditMode ? 'Update Quotation' : 'Create Quotation' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { QuotationService, CustomerService } from '@/services/api'
import type { Quotation, QuotationStatus, Customer } from '@/types'
import SearchableDropdown from '@/components/SearchableDropdown.vue'

interface Props {
  quotation?: Quotation
}

interface Emits {
  (e: 'close'): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isEditMode = computed(() => !!props.quotation)

// Customer data
const customers = ref<Customer[]>([])

const form = ref({
  quotationNo: '',
  title: '',
  description: '',
  leadId: '',
  customerId: '',
  dealId: '',
  status: '' as QuotationStatus,
  version: 1,
  currency: 'MYR',
  subTotal: 0,
  taxAmount: 0,
  taxPercentage: 0,
  discountAmount: 0,
  discountPercentage: 0,
  totalAmount: 0,
  createdBy: '',
  approvedBy: '',
  reviewedBy: '',
  validUntil: '',
  paymentTerms: '',
  deliveryTerms: '',
  warrantyTerms: '',
  specialTerms: '',
  notes: '',
  internalNotes: '',
  items: [] as any[]
})

// Customer search function
const searchCustomers = async (query: string) => {
  try {
    const response = await CustomerService.getCustomers({
      search: query,
      limit: 10
    })

    if (response.success && response.data && response.data.data) {
      customers.value = response.data.data.data || []
    }
  } catch (error) {
    console.error('Error searching customers:', error)
    customers.value = []
  }
}

// Customer change handler
const onCustomerChange = (option: any) => {
  const customer = option as Customer | null
  if (customer) {
    form.value.customerId = customer.id
  } else {
    form.value.customerId = ''
  }
}

// Load initial customers
const loadInitialCustomers = async () => {
  try {
    const response = await CustomerService.getCustomers({
      limit: 10
    })

    if (response.success && response.data && response.data.data) {
      customers.value = response.data.data.data || []
    }
  } catch (error) {
    console.error('Error loading customers:', error)
    customers.value = []
  }
}

// Item management functions
const addItem = () => {
  form.value.items.push({
    itemNo: form.value.items.length + 1,
    serviceType: '',
    category: '',
    name: '',
    description: '',
    specification: '',
    quantity: 1,
    unit: 'pcs',
    unitPrice: 0,
    totalPrice: 0,
    unitCost: 0,
    totalCost: 0,
    margin: 0,
    marginPercentage: 0,
    vendorId: '',
    vendorQuoteNo: '',
    isSubContracted: false,
    deliveryTime: '',
    warrantyPeriod: '',
    notes: '',
    internalNotes: '',
    technicalSpecs: {},
    siteRequirements: '',
    installationRequired: false
  })
}

const removeItem = (index: number) => {
  form.value.items.splice(index, 1)
  // Recalculate item numbers
  form.value.items.forEach((item, idx) => {
    item.itemNo = idx + 1
  })
  calculateQuotationTotals()
}

const calculateItemTotals = (item: any) => {
  // Calculate total price
  item.totalPrice = (item.quantity || 0) * (item.unitPrice || 0)

  // Calculate total cost
  item.totalCost = (item.quantity || 0) * (item.unitCost || 0)

  // Calculate margin
  item.margin = item.totalPrice - item.totalCost

  // Calculate margin percentage
  if (item.totalPrice > 0) {
    item.marginPercentage = (item.margin / item.totalPrice) * 100
  } else {
    item.marginPercentage = 0
  }

  // Recalculate quotation totals
  calculateQuotationTotals()
}

const calculateQuotationTotals = () => {
  // Calculate subtotal from items
  form.value.subTotal = form.value.items.reduce((sum, item) => sum + (item.totalPrice || 0), 0)

  // Calculate discount amount
  if (form.value.discountPercentage > 0) {
    form.value.discountAmount = form.value.subTotal * (form.value.discountPercentage / 100)
  } else {
    form.value.discountAmount = 0
  }

  // Calculate taxable amount
  const taxableAmount = form.value.subTotal - form.value.discountAmount

  // Calculate tax amount
  if (form.value.taxPercentage > 0) {
    form.value.taxAmount = taxableAmount * (form.value.taxPercentage / 100)
  } else {
    form.value.taxAmount = 0
  }

  // Calculate total amount
  form.value.totalAmount = taxableAmount + form.value.taxAmount
}

const handleSubmit = async () => {
  try {
    const quotationData = {
      quotationNo: form.value.quotationNo,
      title: form.value.title,
      description: form.value.description || undefined,
      leadId: form.value.leadId || undefined,
      customerId: form.value.customerId,
      dealId: form.value.dealId || undefined,
      status: form.value.status,
      version: form.value.version,
      currency: form.value.currency,
      subTotal: form.value.subTotal,
      taxAmount: form.value.taxAmount,
      taxPercentage: form.value.taxPercentage,
      discountAmount: form.value.discountAmount,
      discountPercentage: form.value.discountPercentage,
      totalAmount: form.value.totalAmount,
      createdBy: form.value.createdBy,
      approvedBy: form.value.approvedBy || undefined,
      reviewedBy: form.value.reviewedBy || undefined,
      validUntil: form.value.validUntil || undefined,
      paymentTerms: form.value.paymentTerms || undefined,
      deliveryTerms: form.value.deliveryTerms || undefined,
      warrantyTerms: form.value.warrantyTerms || undefined,
      specialTerms: form.value.specialTerms || undefined,
      notes: form.value.notes || undefined,
      internalNotes: form.value.internalNotes || undefined,
      items: form.value.items
    }

    let response
    if (isEditMode.value && props.quotation) {
      response = await QuotationService.updateQuotation(props.quotation.id, quotationData)
    } else {
      response = await QuotationService.createQuotation(quotationData)
    }

    if (response.success && response.data && response.data.success) {
      emit('success')
    } else {
      alert('Failed to save quotation: ' + (response.error || 'Unknown error'))
    }
  } catch (error) {
    alert('Failed to save quotation: ' + (error instanceof Error ? error.message : 'Unknown error'))
  }
}

// Initialize form with quotation data when in edit mode
onMounted(async () => {
  // Load initial customers
  await loadInitialCustomers()

  if (isEditMode.value && props.quotation) {
    form.value = {
      quotationNo: props.quotation.quotationNo,
      title: props.quotation.title,
      description: props.quotation.description || '',
      leadId: props.quotation.leadId || '',
      customerId: props.quotation.customerId,
      dealId: props.quotation.dealId || '',
      status: props.quotation.status,
      version: props.quotation.version,
      currency: props.quotation.currency,
      subTotal: props.quotation.subTotal || 0,
      taxAmount: props.quotation.taxAmount || 0,
      taxPercentage: props.quotation.taxPercentage || 0,
      discountAmount: props.quotation.discountAmount || 0,
      discountPercentage: props.quotation.discountPercentage || 0,
      totalAmount: props.quotation.totalAmount || 0,
      createdBy: props.quotation.createdBy,
      approvedBy: props.quotation.approvedBy || '',
      reviewedBy: props.quotation.reviewedBy || '',
      validUntil: props.quotation.validUntil ? props.quotation.validUntil.split('T')[0] : '',
      paymentTerms: props.quotation.paymentTerms || '',
      deliveryTerms: props.quotation.deliveryTerms || '',
      warrantyTerms: props.quotation.warrantyTerms || '',
      specialTerms: props.quotation.specialTerms || '',
      notes: props.quotation.notes || '',
      internalNotes: props.quotation.internalNotes || '',
      items: props.quotation.items || []
    }
  }
})
</script>
