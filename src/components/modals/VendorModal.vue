<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Vendor' : 'Add New Vendor' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p class="text-sm text-red-800">{{ error }}</p>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Vendor Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">
              Vendor Name <span class="text-red-500">*</span>
            </label>
            <input
              id="name"
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter vendor name"
            />
          </div>

          <!-- Contact Person -->
          <div>
            <label for="contactPerson" class="block text-sm font-medium text-gray-700 mb-1">
              Contact Person
            </label>
            <input
              id="contactPerson"
              v-model="form.contactPerson"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter contact person name"
            />
          </div>

          <!-- Contact Number -->
          <div>
            <label for="contactNo" class="block text-sm font-medium text-gray-700 mb-1">
              Contact Number
            </label>
            <input
              id="contactNo"
              v-model="form.contactNo"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter contact number"
            />
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter email address"
            />
          </div>

          <!-- Address -->
          <div>
            <label for="address" class="block text-sm font-medium text-gray-700 mb-1">
              Address
            </label>
            <textarea
              id="address"
              v-model="form.address"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter vendor address"
            ></textarea>
          </div>

          <!-- Remarks -->
          <div>
            <label for="remark" class="block text-sm font-medium text-gray-700 mb-1">
              Remarks
            </label>
            <textarea
              id="remark"
              v-model="form.remark"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter any additional remarks"
            ></textarea>
          </div>

          <div class="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {{ loading ? 'Saving...' : (isEditMode ? 'Update Vendor' : 'Add Vendor') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { VendorService } from '@/services/api'
import type { Vendor, CreateVendorRequest, UpdateVendorRequest } from '@/types'

const props = defineProps<{
  vendor?: Vendor
}>()

const emit = defineEmits<{
  close: []
  success: []
}>()

const isEditMode = computed(() => !!props.vendor)
const loading = ref(false)
const error = ref<string | null>(null)

const form = ref({
  name: '',
  contactPerson: '',
  contactNo: '',
  email: '',
  address: '',
  remark: ''
})

// Initialize form with vendor data if editing
onMounted(() => {
  if (props.vendor) {
    form.value = {
      name: props.vendor.name || '',
      contactPerson: props.vendor.contactPerson || '',
      contactNo: props.vendor.contactNo || '',
      email: props.vendor.email || '',
      address: props.vendor.address || '',
      remark: props.vendor.remark || ''
    }
  }
})

const handleSubmit = async () => {
  loading.value = true
  error.value = null

  try {
    const response = isEditMode.value
      ? await VendorService.updateVendor(props.vendor!.id, form.value as UpdateVendorRequest)
      : await VendorService.createVendor(form.value as CreateVendorRequest)

    if (response.success) {
      emit('success')
      emit('close')
    } else {
      error.value = response.error || `Failed to ${isEditMode.value ? 'update' : 'create'} vendor`
    }
  } catch (err) {
    error.value = `Failed to ${isEditMode.value ? 'update' : 'create'} vendor`
    console.error(`${isEditMode.value ? 'Update' : 'Create'} vendor error:`, err)
  } finally {
    loading.value = false
  }
}
</script>
