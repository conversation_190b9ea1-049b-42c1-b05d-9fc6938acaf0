<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Deal' : 'Add New Deal' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Basic Deal Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Basic Deal Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="dealNo" class="block text-sm font-medium text-gray-700 mb-1">Deal Number *</label>
                <input
                  id="dealNo"
                  v-model="form.dealNo"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter deal number"
                />
              </div>
              <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
                <input
                  id="title"
                  v-model="form.title"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter deal title"
                />
              </div>
              <div class="md:col-span-2">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  id="description"
                  v-model="form.description"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter deal description"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Related Records -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Related Records</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="customerId" class="block text-sm font-medium text-gray-700 mb-1">Customer *</label>
                <SearchableDropdown
                  id="customerId"
                  v-model="form.customerId"
                  :options="customers"
                  label-key="name"
                  value-key="id"
                  placeholder="Search customers..."
                  :required="true"
                  :search-function="searchCustomers"
                  @change="onCustomerChange"
                />
              </div>
              <div>
                <label for="leadId" class="block text-sm font-medium text-gray-700 mb-1">Original Lead</label>
                <select
                  id="leadId"
                  v-model="form.leadId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select lead</option>
                  <!-- TODO: Load leads dynamically -->
                  <option value="lead-1">Lead 1</option>
                  <option value="lead-2">Lead 2</option>
                </select>
              </div>
              <div>
                <label for="quotationId" class="block text-sm font-medium text-gray-700 mb-1">Quotation</label>
                <select
                  id="quotationId"
                  v-model="form.quotationId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select quotation</option>
                  <!-- TODO: Load quotations dynamically -->
                  <option value="quote-1">Quote 1</option>
                  <option value="quote-2">Quote 2</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Deal Classification -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Deal Classification</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                <select
                  id="status"
                  v-model="form.status"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select status</option>
                  <option value="draft">Draft</option>
                  <option value="pending_presales">Pending Presales</option>
                  <option value="presales_review">Presales Review</option>
                  <option value="technical_review">Technical Review</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="on_hold">On Hold</option>
                  <option value="closed">Closed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>
              <div>
                <label for="stage" class="block text-sm font-medium text-gray-700 mb-1">Stage *</label>
                <select
                  id="stage"
                  v-model="form.stage"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select stage</option>
                  <option value="initial">Initial</option>
                  <option value="site_validation">Site Validation</option>
                  <option value="technical_proposal">Technical Proposal</option>
                  <option value="feasibility_check">Feasibility Check</option>
                  <option value="hardware_procurement">Hardware Procurement</option>
                  <option value="vendor_quotes">Vendor Quotes</option>
                  <option value="pnl_review">P&L Review</option>
                  <option value="final_approval">Final Approval</option>
                  <option value="implementation">Implementation</option>
                </select>
              </div>
              <div>
                <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Priority *</label>
                <select
                  id="priority"
                  v-model="form.priority"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select priority</option>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Financial Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Financial Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label for="totalValue" class="block text-sm font-medium text-gray-700 mb-1">Total Value *</label>
                <input
                  id="totalValue"
                  v-model.number="form.totalValue"
                  type="number"
                  min="0"
                  step="0.01"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter total value"
                />
              </div>
              <div>
                <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">Currency *</label>
                <select
                  id="currency"
                  v-model="form.currency"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select currency</option>
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="MYR">MYR</option>
                  <option value="SGD">SGD</option>
                </select>
              </div>
              <div>
                <label for="expectedMargin" class="block text-sm font-medium text-gray-700 mb-1">Expected Margin (%) *</label>
                <input
                  id="expectedMargin"
                  v-model.number="form.expectedMargin"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter expected margin"
                />
              </div>
              <div>
                <label for="actualMargin" class="block text-sm font-medium text-gray-700 mb-1">Actual Margin (%)</label>
                <input
                  id="actualMargin"
                  v-model.number="form.actualMargin"
                  type="number"
                  min="0"
                  max="100"
                  step="0.01"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter actual margin"
                />
              </div>
            </div>
          </div>

          <!-- Assignment and Ownership -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Assignment and Ownership</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label for="salesExecutive" class="block text-sm font-medium text-gray-700 mb-1">Sales Executive *</label>
                <select
                  id="salesExecutive"
                  v-model="form.salesExecutive"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select sales executive</option>
                  <!-- TODO: Load users dynamically -->
                  <option value="user-1">Sales Executive 1</option>
                  <option value="user-2">Sales Executive 2</option>
                </select>
              </div>
              <div>
                <label for="presalesEngineer" class="block text-sm font-medium text-gray-700 mb-1">Presales Engineer</label>
                <select
                  id="presalesEngineer"
                  v-model="form.presalesEngineer"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select presales engineer</option>
                  <!-- TODO: Load users dynamically -->
                  <option value="user-3">Presales Engineer 1</option>
                  <option value="user-4">Presales Engineer 2</option>
                </select>
              </div>
              <div>
                <label for="salesManager" class="block text-sm font-medium text-gray-700 mb-1">Sales Manager</label>
                <select
                  id="salesManager"
                  v-model="form.salesManager"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select sales manager</option>
                  <!-- TODO: Load users dynamically -->
                  <option value="user-5">Sales Manager 1</option>
                  <option value="user-6">Sales Manager 2</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Timeline -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Timeline</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label for="submittedDate" class="block text-sm font-medium text-gray-700 mb-1">Submitted Date</label>
                <input
                  id="submittedDate"
                  v-model="form.submittedDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="approvedDate" class="block text-sm font-medium text-gray-700 mb-1">Approved Date</label>
                <input
                  id="approvedDate"
                  v-model="form.approvedDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="expectedCloseDate" class="block text-sm font-medium text-gray-700 mb-1">Expected Close Date</label>
                <input
                  id="expectedCloseDate"
                  v-model="form.expectedCloseDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="actualCloseDate" class="block text-sm font-medium text-gray-700 mb-1">Actual Close Date</label>
                <input
                  id="actualCloseDate"
                  v-model="form.actualCloseDate"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          <!-- Technical Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Technical Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <label class="flex items-center">
                <input
                  type="checkbox"
                  v-model="form.siteValidated"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">Site Validated</span>
              </label>
              <label class="flex items-center">
                <input
                  type="checkbox"
                  v-model="form.coverageChecked"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">Coverage Checked</span>
              </label>
              <label class="flex items-center">
                <input
                  type="checkbox"
                  v-model="form.infraValidated"
                  class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span class="ml-2 text-sm text-gray-700">Infrastructure Validated</span>
              </label>
            </div>
            <div>
              <label for="technicalNotes" class="block text-sm font-medium text-gray-700 mb-1">Technical Notes</label>
              <textarea
                id="technicalNotes"
                v-model="form.technicalNotes"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter technical notes"
              ></textarea>
            </div>
          </div>

          <!-- Additional Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Additional Information</h4>
            <div class="grid grid-cols-1 gap-4">
              <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                <textarea
                  id="notes"
                  v-model="form.notes"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter additional notes"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ isEditMode ? 'Update Deal' : 'Create Deal' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { DealService, CustomerService } from '@/services/api'
import type { Deal, DealStatus, DealStage, LeadPriority, Customer } from '@/types'
import SearchableDropdown from '@/components/SearchableDropdown.vue'

interface Props {
  deal?: Deal
}

interface Emits {
  (e: 'close'): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const isEditMode = computed(() => !!props.deal)

// Customer data
const customers = ref<Customer[]>([])

const form = ref({
  dealNo: '',
  title: '',
  description: '',
  leadId: '',
  customerId: '',
  quotationId: '',
  status: '' as DealStatus,
  stage: '' as DealStage,
  priority: '' as LeadPriority,
  totalValue: 0,
  currency: '',
  expectedMargin: 0,
  actualMargin: 0,
  salesExecutive: '',
  presalesEngineer: '',
  salesManager: '',
  submittedDate: '',
  approvedDate: '',
  expectedCloseDate: '',
  actualCloseDate: '',
  siteValidated: false,
  coverageChecked: false,
  infraValidated: false,
  technicalNotes: '',
  notes: ''
})

// Customer search function
const searchCustomers = async (query: string) => {
  try {
    const response = await CustomerService.getCustomers({
      search: query,
      limit: 10
    })

    if (response.success && response.data && response.data.data) {
      customers.value = response.data.data.data || []
    }
  } catch (error) {
    console.error('Error searching customers:', error)
    customers.value = []
  }
}

// Customer change handler
const onCustomerChange = (option: any) => {
  const customer = option as Customer | null
  if (customer) {
    form.value.customerId = customer.id
  } else {
    form.value.customerId = ''
  }
}

// Load initial customers
const loadInitialCustomers = async () => {
  try {
    const response = await CustomerService.getCustomers({
      limit: 10
    })

    if (response.success && response.data && response.data.data) {
      customers.value = response.data.data.data || []
    }
  } catch (error) {
    console.error('Error loading customers:', error)
    customers.value = []
  }
}

const handleSubmit = async () => {
  try {
    const dealData = {
      dealNo: form.value.dealNo,
      title: form.value.title,
      description: form.value.description,
      leadId: form.value.leadId || undefined,
      customerId: form.value.customerId,
      quotationId: form.value.quotationId || undefined,
      status: form.value.status,
      stage: form.value.stage,
      priority: form.value.priority,
      totalValue: form.value.totalValue,
      currency: form.value.currency,
      expectedMargin: form.value.expectedMargin,
      actualMargin: form.value.actualMargin,
      services: [], // TODO: Add services management
      salesExecutive: form.value.salesExecutive,
      presalesEngineer: form.value.presalesEngineer || undefined,
      salesManager: form.value.salesManager || undefined,
      submittedDate: form.value.submittedDate || undefined,
      approvedDate: form.value.approvedDate || undefined,
      expectedCloseDate: form.value.expectedCloseDate || undefined,
      actualCloseDate: form.value.actualCloseDate || undefined,
      siteValidated: form.value.siteValidated,
      coverageChecked: form.value.coverageChecked,
      infraValidated: form.value.infraValidated,
      technicalNotes: form.value.technicalNotes,
      hardwareRequired: false,
      hardwareItems: [],
      vendorQuotes: [],
      subContractRequired: false,
      subContractors: [],
      notes: form.value.notes,
      tags: [],
      attachments: []
    }

    let response
    if (isEditMode.value && props.deal) {
      response = await DealService.updateDeal(props.deal.id, dealData)
    } else {
      response = await DealService.createDeal(dealData)
    }

    if (response.success && response.data && response.data.success) {
      emit('success')
      emit('close')
    } else {
      const action = isEditMode.value ? 'update' : 'create'
      alert(`Failed to ${action} deal: ` + (response.error || 'Unknown error'))
    }
  } catch (error) {
    const action = isEditMode.value ? 'update' : 'create'
    alert(`Failed to ${action} deal: ` + (error instanceof Error ? error.message : 'Unknown error'))
  }
}

// Initialize form with deal data when in edit mode
onMounted(async () => {
  // Load initial customers
  await loadInitialCustomers()

  if (isEditMode.value && props.deal) {
    form.value = {
      dealNo: props.deal.dealNo,
      title: props.deal.title,
      description: props.deal.description || '',
      leadId: props.deal.leadId || '',
      customerId: props.deal.customerId,
      quotationId: props.deal.quotationId || '',
      status: props.deal.status,
      stage: props.deal.stage,
      priority: props.deal.priority,
      totalValue: props.deal.totalValue || 0,
      currency: props.deal.currency,
      expectedMargin: props.deal.expectedMargin || 0,
      actualMargin: props.deal.actualMargin || 0,
      salesExecutive: props.deal.salesExecutive,
      presalesEngineer: props.deal.presalesEngineer || '',
      salesManager: props.deal.salesManager || '',
      submittedDate: props.deal.submittedDate ? props.deal.submittedDate.split('T')[0] : '',
      approvedDate: props.deal.approvedDate ? props.deal.approvedDate.split('T')[0] : '',
      expectedCloseDate: props.deal.expectedCloseDate ? props.deal.expectedCloseDate.split('T')[0] : '',
      actualCloseDate: props.deal.actualCloseDate ? props.deal.actualCloseDate.split('T')[0] : '',
      siteValidated: props.deal.siteValidated || false,
      coverageChecked: props.deal.coverageChecked || false,
      infraValidated: props.deal.infraValidated || false,
      technicalNotes: props.deal.technicalNotes || '',
      notes: props.deal.notes || ''
    }
  }
})
</script>
