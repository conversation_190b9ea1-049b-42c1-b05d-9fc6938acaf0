// API Response Types
export interface UserResponse {
  id: string
  name: string
  login: string
  email: string
  role: string
  contactNo: string
  notification: boolean
  created_at: string
  updated_at: string
}

export interface CreateUserRequest {
  name: string
  login: string
  email: string
  password: string
  role: string
  contactNo: string
  notification?: boolean
}

export interface UpdateUserRequest extends Partial<Omit<CreateUserRequest, 'password'>> {
  password?: string
}

export interface Customer {
  id: string
  created_at: string
  updated_at: string
  parentId?: string | null
  name: string
  contactPerson: string
  contactPerson2?: string
  contactNo: string
  contactNo2?: string
  email: string
  email2?: string
  address1: string
  address2?: string
  postcode: string
  city: string
  state: string
  country: string
  createBy: string
  isDelete: boolean
  officeNo?: string
  officeNo2?: string
  fax?: string
  officeExtNo?: string
  officeExtNo2?: string
  fullName?: string
}

// API Request Types
export interface CreateCustomerRequest {
  parentId?: string
  name: string
  contactPerson: string
  contactPerson2?: string
  contactNo: string
  contactNo2?: string
  email: string
  email2?: string
  address1: string
  address2?: string
  postcode: string
  city: string
  state: string
  country: string
  officeNo?: string
  officeNo2?: string
  fax?: string
  officeExtNo?: string
  officeExtNo2?: string
  fullName?: string
}

export interface UpdateCustomerRequest extends Partial<CreateCustomerRequest> {}

// Query parameters for paginated requests
export interface PaginationQuery {
  limit?: number
  page?: number
  search?: string
}

// Pagination information
export interface PaginationInfo {
  limit: number
  page: number
  total: number
}

// Paginated response structure (for list endpoints)
export interface PaginatedResponse<T> {
  data: T[]
  limit: number
  page: number
  total: number
}

// Inner API response structure (what the server actually returns)
export interface InnerApiResponse<T> {
  success: boolean
  data: T | null
  pagination?: PaginationInfo
  timestamp: string
}

// Outer API response wrapper (what we get from the HTTP request)
export interface ApiResponse<T> {
  success: boolean
  data?: InnerApiResponse<T>
  message?: string
  error?: string
}

export interface Equipment {
  id: string
  vendorId: string
  category0: string
  category1: string
  category2: string
  orderId: string
  name: string
  sn?: string
  warrantyPeriod?: number
  warrantyStartDate?: string
  warrantyExpDate?: string
  status: number
  remark?: string
  stockinDate?: string
  stockinBy?: string
  stockoutDate?: string
  stockoutBy?: string
  createBy: string
  isDelete: boolean
  created_at: string
  updated_at: string
}

export interface CreateEquipmentRequest {
  vendorId: string
  category0: string
  category1: string
  category2: string
  orderId: string
  name: string
  sn?: string
  warrantyPeriod?: number
  warrantyStartDate?: string
  warrantyExpDate?: string
  status: number
  remark?: string
  stockinDate?: string
  stockinBy?: string
  stockoutDate?: string
  stockoutBy?: string
}

export interface UpdateEquipmentRequest extends Partial<CreateEquipmentRequest> {}

export interface Event {
  id: string
  title: string
  description: string
  date: Date
  type: 'event' | 'equipment-expiry' | 'reminder'
  color: 'blue' | 'red' | 'green'
  equipmentId?: string
  customerId?: string
  created_at: Date
  updated_at: Date
}

export interface Ticket {
  id: string
  ticketno: string
  title: string
  description?: string
  level?: string
  customerPackage?: string
  department?: string
  assignee?: string[]
  attachments?: string[]
  createBy: string
  customer: string
  closingdate?: string
  statustxt?: string
  created_at: string
  updated_at: string
}

export interface CreateTicketRequest {
  ticketno: string
  title: string
  description?: string
  level?: string
  customerPackage?: string
  department?: string
  assignee?: string[]
  attachments?: string[]
  customer: string
  statustxt?: string
}

export interface UpdateTicketRequest extends Partial<CreateTicketRequest> {}

export interface CalendarItem {
  id: string
  title: string
  date: Date
  type: 'event' | 'equipment-expiry' | 'reminder'
  color: 'blue' | 'red' | 'green'
  relatedId?: string
}

// Customer Package Types
export interface CustomerPackage {
  id: string
  customerId: string
  packageId: string
  circuitId?: string
  wanIp?: string
  lanIp?: string
  servicesNo?: string
  loginId?: string
  password?: string
  accountNo?: string
  orderNo?: string
  speed?: string
  provider?: string
  brandModem?: string
  modemLogin?: string
  remark?: string
  createBy: string
  isDelete: boolean
  companyRegNo?: string
  companyName?: string
  serviceTag?: string
  orderStageId?: string
  activationDate?: string
  contractEndDate?: string
  status: number
  terminateAlertFrom?: string
  terminateAlertTo?: string
  isNotify: boolean
  created_at: string
  updated_at: string
}

export interface CreateCustomerPackageRequest {
  customerId: string
  packageId: string
  circuitId?: string
  wanIp?: string
  lanIp?: string
  servicesNo?: string
  loginId?: string
  password?: string
  accountNo?: string
  orderNo?: string
  speed?: string
  provider?: string
  brandModem?: string
  modemLogin?: string
  remark?: string
  companyRegNo?: string
  companyName?: string
  serviceTag?: string
  orderStageId?: string
  activationDate?: string
  contractEndDate?: string
  status: number
  terminateAlertFrom?: string
  terminateAlertTo?: string
  isNotify?: boolean
}

export interface UpdateCustomerPackageRequest extends Partial<CreateCustomerPackageRequest> {}

// Customer Document Types
export interface CustomerDocument {
  id: string
  customerId: string
  fileId: string
  docType: number
  name: string
  remark?: string
  status: number
  createBy: string
  isDelete: boolean
  created_at: string
  updated_at: string
  // File properties (populated from fileId)
  fileName?: string
  originalName?: string
  size?: number
  mimeType?: string
  s3Url?: string
}

export interface CreateCustomerDocumentRequest {
  customerId: string
  fileId: string
  docType: number
  name: string
  remark?: string
  status: number
}

export interface UpdateCustomerDocumentRequest extends Partial<CreateCustomerDocumentRequest> {}

// File Types
export interface File {
  id: string
  originalName: string
  fileName: string
  mimeType: string
  size: number
  s3Key: string
  s3Bucket: string
  s3Url: string
  uploadedBy: string
  isDelete: boolean
  metadata?: Record<string, string>
  created_at: string
  updated_at: string
}

export interface CreateFileRequest {
  originalName: string
  fileName: string
  mimeType: string
  size: number
  s3Key: string
  s3Bucket: string
  s3Url: string
  uploadedBy: string
  metadata?: Record<string, string>
}

export interface UpdateFileRequest extends Partial<CreateFileRequest> {}

// Package Types
export interface Package {
  id: string
  name: string
  createBy: string
  isDelete: boolean
  created_at: string
  updated_at: string
}

export interface CreatePackageRequest {
  name: string
}

export interface UpdatePackageRequest extends Partial<CreatePackageRequest> {}

// Customer Services Types
export interface CustomerServices {
  id: string
  ticketNo?: string
  customerId: string
  status: number
  reportBy?: string
  reportContact?: string
  outageCategory?: string
  circuitId?: string
  telcoReportNo?: string
  telcoPerson?: string
  telcoContact?: string
  createBy: string
  isDelete: boolean
  packageId?: string
  created_at: string
  updated_at: string
}

export interface CreateCustomerServicesRequest {
  ticketNo?: string
  customerId: string
  status: number
  reportBy?: string
  reportContact?: string
  outageCategory?: string
  circuitId?: string
  telcoReportNo?: string
  telcoPerson?: string
  telcoContact?: string
  packageId?: string
}

export interface UpdateCustomerServicesRequest extends Partial<CreateCustomerServicesRequest> {}

// Equipment Inventory Types
export interface EquipmentInventory {
  id: string
  equipmentId: string
  customerId: string
  orderId?: string
  sn?: string
  status: number
  remark?: string
  stockinDate?: string
  stockinBy?: string
  stockoutDate?: string
  stockoutBy?: string
  createBy: string
  isDelete: boolean
  warrantyPeriod?: number
  warrantyStartDate?: string
  warrantyExpDate?: string
  orderStageId?: string
  orderStage2EquipmentId?: string
  maintenanceVendorId?: string
  maintenanceExpired?: string
  created_at: string
  updated_at: string
}

export interface CreateEquipmentInventoryRequest {
  equipmentId: string
  customerId: string
  orderId?: string
  sn?: string
  status: number
  remark?: string
  stockinDate?: string
  stockinBy?: string
  stockoutDate?: string
  stockoutBy?: string
  warrantyPeriod?: number
  warrantyStartDate?: string
  warrantyExpDate?: string
  orderStageId?: string
  orderStage2EquipmentId?: string
  maintenanceVendorId?: string
  maintenanceExpired?: string
}

export interface UpdateEquipmentInventoryRequest extends Partial<CreateEquipmentInventoryRequest> {}

// Equipment Category Types
export interface EquipmentCategory {
  id: string
  parentId?: string
  name: string
  categoryType: number
  isDelete: boolean
  created_at: string
  updated_at: string
}

export interface CreateEquipmentCategoryRequest {
  parentId?: string
  name: string
  categoryType: number
}

export interface UpdateEquipmentCategoryRequest extends Partial<CreateEquipmentCategoryRequest> {}

// Vendor Types
export interface Vendor {
  id: string
  name: string
  contactPerson?: string
  contactNo?: string
  email?: string
  address?: string
  remark?: string
  createBy: string
  isDelete: boolean
  created_at: string
  updated_at: string
}

export interface CreateVendorRequest {
  name: string
  contactPerson?: string
  contactNo?: string
  email?: string
  address?: string
  remark?: string
}

export interface UpdateVendorRequest extends Partial<CreateVendorRequest> {}

// Role Types
export interface Role {
  id: string
  name: string
  description?: string
  isDelete: boolean
  created_at: string
  updated_at: string
}

export interface CreateRoleRequest {
  name: string
  description?: string
}

export interface UpdateRoleRequest extends Partial<CreateRoleRequest> {}

// Lead Enums
export enum LeadSource {
  WEBSITE = 'website',
  REFERRAL = 'referral',
  COLD_CALL = 'cold_call',
  EMAIL_CAMPAIGN = 'email_campaign',
  SOCIAL_MEDIA = 'social_media',
  TRADE_SHOW = 'trade_show',
  PARTNER = 'partner',
  OTHER = 'other'
}

export enum LeadPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export enum LeadStatus {
  NEW = 'new',
  CONTACTED = 'contacted',
  QUALIFIED = 'qualified',
  PROPOSAL = 'proposal',
  NEGOTIATION = 'negotiation',
  CLOSED_WON = 'closed_won',
  CLOSED_LOST = 'closed_lost',
  NURTURING = 'nurturing'
}

export enum ServiceType {
  INTERNET = 'internet',
  NETWORKING = 'networking',
  SECURITY = 'security',
  CLOUD = 'cloud',
  MAINTENANCE = 'maintenance',
  CONSULTATION = 'consultation',
  OTHER = 'other'
}

// Lead Types
export interface Lead {
  id: string
  created_at: string
  updated_at: string

  // Basic Lead Information
  leadNo: string
  companyName: string
  contactPerson: string
  contactEmail: string
  contactPhone: string
  alternatePhone?: string

  // Address Information
  address1: string
  address2?: string
  city: string
  state: string
  postcode: string
  country: string

  // Lead Classification
  source: LeadSource
  priority: LeadPriority
  status: LeadStatus

  // Service Requirements
  servicesRequired: ServiceType[]
  estimatedValue: number
  currency: string

  // Assignment and Ownership
  assignedTo?: string // Sales Executive ID
  assignedBy?: string // Sales Manager ID who assigned
  assignedDate?: string

  // Timeline and Follow-up
  nextFollowUp?: string
  lastContactDate?: string
  expectedCloseDate?: string

  // Additional Information
  description?: string
  notes?: string
  tags?: string[]

  // System Fields
  createBy: string
  isDelete: boolean

  // Conversion tracking
  convertedToDeal?: string
  convertedDate?: string
}

export interface CreateLeadRequest {
  // Basic Lead Information
  leadNo: string
  companyName: string
  contactPerson: string
  contactEmail: string
  contactPhone: string
  alternatePhone?: string

  // Address Information
  address1: string
  address2?: string
  city: string
  state: string
  postcode: string
  country: string

  // Lead Classification
  source: LeadSource
  priority: LeadPriority
  status: LeadStatus

  // Service Requirements
  servicesRequired: ServiceType[]
  estimatedValue: number
  currency: string

  // Assignment and Ownership
  assignedTo?: string
  assignedBy?: string
  assignedDate?: string

  // Timeline and Follow-up
  nextFollowUp?: string
  lastContactDate?: string
  expectedCloseDate?: string

  // Additional Information
  description?: string
  notes?: string
  tags?: string[]

  // Conversion tracking
  convertedToDeal?: string
  convertedDate?: string
}

export interface UpdateLeadRequest extends Partial<CreateLeadRequest> {}

// Deal Enums
export enum DealStatus {
  DRAFT = 'draft',
  PENDING_PRESALES = 'pending_presales',
  PRESALES_REVIEW = 'presales_review',
  TECHNICAL_REVIEW = 'technical_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  ON_HOLD = 'on_hold',
  CLOSED = 'closed',
  CANCELLED = 'cancelled'
}

export enum DealStage {
  INITIAL = 'initial',
  SITE_VALIDATION = 'site_validation',
  TECHNICAL_PROPOSAL = 'technical_proposal',
  FEASIBILITY_CHECK = 'feasibility_check',
  HARDWARE_PROCUREMENT = 'hardware_procurement',
  VENDOR_QUOTES = 'vendor_quotes',
  PNL_REVIEW = 'pnl_review',
  FINAL_APPROVAL = 'final_approval',
  IMPLEMENTATION = 'implementation'
}

// Deal Service Interface
export interface DealService {
  serviceType: ServiceType
  description: string
  quantity: number
  unitPrice: number
  totalPrice: number
  cost: number
  margin: number
  notes?: string
}

// Hardware Item Interface
export interface HardwareItem {
  category: string
  name: string
  specification?: string
  quantity: number
  estimatedCost: number
  actualCost: number
  vendorId?: string
  status?: string // pending, ordered, received, installed
  notes?: string
}

// Vendor Quote Interface
export interface VendorQuote {
  vendorId: string
  quoteNo?: string
  quoteDate: string
  totalAmount: number
  validUntil?: string
  terms?: string
  isSelected: boolean
  notes?: string
  attachmentId?: string
}

// Sub Contractor Interface
export interface SubContractor {
  vendorId: string
  serviceType: ServiceType
  description: string
  estimatedCost: number
  actualCost: number
  status?: string // pending, contracted, in_progress, completed
  contractDate?: string
  completionDate?: string
  notes?: string
}

// Deal Interface
export interface Deal {
  id: string
  created_at: string
  updated_at: string

  // Basic Deal Information
  dealNo: string
  title: string
  description?: string

  // Related Records
  leadId?: string
  customerId: string
  quotationId?: string

  // Deal Classification
  status: DealStatus
  stage: DealStage
  priority: LeadPriority

  // Financial Information
  totalValue: number
  currency: string
  expectedMargin: number
  actualMargin: number

  // Service Breakdown
  services: DealService[]

  // Assignment and Ownership
  salesExecutive: string
  presalesEngineer?: string
  salesManager?: string

  // Timeline
  submittedDate?: string
  approvedDate?: string
  expectedCloseDate?: string
  actualCloseDate?: string

  // Technical Information
  siteValidated: boolean
  coverageChecked: boolean
  infraValidated: boolean
  technicalNotes?: string

  // Hardware and Procurement
  hardwareRequired: boolean
  hardwareItems: HardwareItem[]
  vendorQuotes: VendorQuote[]

  // Sub-contracting
  subContractRequired: boolean
  subContractors: SubContractor[]

  // Additional Information
  notes?: string
  tags?: string[]
  attachments?: string[]

  // System Fields
  createBy: string
  isDelete: boolean
}

export interface CreateDealRequest {
  // Basic Deal Information
  dealNo: string
  title: string
  description?: string

  // Related Records
  leadId?: string
  customerId: string
  quotationId?: string

  // Deal Classification
  status: DealStatus
  stage: DealStage
  priority: LeadPriority

  // Financial Information
  totalValue: number
  currency: string
  expectedMargin: number
  actualMargin?: number

  // Service Breakdown
  services: DealService[]

  // Assignment and Ownership
  salesExecutive: string
  presalesEngineer?: string
  salesManager?: string

  // Timeline
  submittedDate?: string
  approvedDate?: string
  expectedCloseDate?: string
  actualCloseDate?: string

  // Technical Information
  siteValidated?: boolean
  coverageChecked?: boolean
  infraValidated?: boolean
  technicalNotes?: string

  // Hardware and Procurement
  hardwareRequired?: boolean
  hardwareItems?: HardwareItem[]
  vendorQuotes?: VendorQuote[]

  // Sub-contracting
  subContractRequired?: boolean
  subContractors?: SubContractor[]

  // Additional Information
  notes?: string
  tags?: string[]
  attachments?: string[]
}

export interface UpdateDealRequest extends Partial<CreateDealRequest> {}

// Quotation Enums
export enum QuotationStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  SENT = 'sent',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  EXPIRED = 'expired',
  REVISION = 'revision'
}

// Quotation Item Interface
export interface QuotationItem {
  itemNo: number
  serviceType: ServiceType
  category: string
  name: string
  description: string
  specification?: string

  // Quantity and Pricing
  quantity: number
  unit: string
  unitPrice: number
  totalPrice: number

  // Cost Analysis
  unitCost: number
  totalCost: number
  margin: number // Amount
  marginPercentage: number // Percentage

  // Vendor Information (for hardware/sub-contracted items)
  vendorId?: string
  vendorQuoteNo?: string
  isSubContracted: boolean

  // Delivery and Warranty
  deliveryTime?: string
  warrantyPeriod?: string

  // Additional Information
  notes?: string
  internalNotes?: string

  // Technical Requirements (for presales validation)
  technicalSpecs?: Record<string, any>
  siteRequirements?: string
  installationRequired: boolean
}

// Quotation Interface
export interface Quotation {
  id: string
  created_at: string
  updated_at: string

  // Basic Quotation Information
  quotationNo: string
  title: string
  description?: string

  // Related Records
  leadId?: string
  customerId: string
  dealId?: string

  // Quotation Classification
  status: QuotationStatus
  version: number
  parentQuotationId?: string // For revisions

  // Financial Information
  subTotal: number
  taxAmount: number
  taxPercentage: number
  discountAmount: number
  discountPercentage: number
  totalAmount: number
  currency: string

  // Cost and Margin Analysis
  totalCost: number
  grossMargin: number // Amount
  grossMarginPercentage: number // Percentage

  // Quotation Items
  items: QuotationItem[]

  // Assignment and Approval
  createdBy: string // Sales Executive
  approvedBy?: string // Sales Manager
  reviewedBy?: string // Presales Engineer

  // Timeline
  createdDate: string
  approvedDate?: string
  sentDate?: string
  validUntil?: string
  responseDate?: string

  // Terms and Conditions
  paymentTerms?: string
  deliveryTerms?: string
  warrantyTerms?: string
  specialTerms?: string

  // Additional Information
  notes?: string
  internalNotes?: string // Not visible to customer
  tags?: string[]
  attachments?: string[] // File IDs

  // Customer Response
  customerFeedback?: string
  rejectionReason?: string

  // System Fields
  createBy: string
  isDelete: boolean
}

// Create Quotation Request
export interface CreateQuotationRequest {
  // Basic Quotation Information
  quotationNo: string
  title: string
  description?: string

  // Related Records
  leadId?: string
  customerId: string
  dealId?: string

  // Quotation Classification
  status: QuotationStatus
  version?: number
  parentQuotationId?: string

  // Financial Information
  subTotal?: number
  taxAmount?: number
  taxPercentage?: number
  discountAmount?: number
  discountPercentage?: number
  totalAmount?: number
  currency: string

  // Cost and Margin Analysis
  totalCost?: number
  grossMargin?: number
  grossMarginPercentage?: number

  // Quotation Items
  items: Omit<QuotationItem, 'id'>[]

  // Assignment and Approval
  createdBy: string
  approvedBy?: string
  reviewedBy?: string

  // Timeline
  createdDate?: string
  approvedDate?: string
  sentDate?: string
  validUntil?: string
  responseDate?: string

  // Terms and Conditions
  paymentTerms?: string
  deliveryTerms?: string
  warrantyTerms?: string
  specialTerms?: string

  // Additional Information
  notes?: string
  internalNotes?: string
  tags?: string[]
  attachments?: string[]

  // Customer Response
  customerFeedback?: string
  rejectionReason?: string
}

// Create Quotation Item Request
export interface CreateQuotationItemRequest {
  itemNo: number
  serviceType: ServiceType
  category: string
  name: string
  description: string
  specification?: string

  // Quantity and Pricing
  quantity: number
  unit: string
  unitPrice: number
  totalPrice?: number

  // Cost Analysis
  unitCost: number
  totalCost?: number
  margin?: number
  marginPercentage?: number

  // Vendor Information
  vendorId?: string
  vendorQuoteNo?: string
  isSubContracted?: boolean

  // Delivery and Warranty
  deliveryTime?: string
  warrantyPeriod?: string

  // Additional Information
  notes?: string
  internalNotes?: string

  // Technical Requirements
  technicalSpecs?: Record<string, any>
  siteRequirements?: string
  installationRequired?: boolean
}

// Update Quotation Request
export interface UpdateQuotationRequest extends Partial<CreateQuotationRequest> {}

// Update Quotation Item Request
export interface UpdateQuotationItemRequest extends Partial<CreateQuotationItemRequest> {}
